<?php

declare(strict_types=1);

/**
 * @copyright  2020 Ad Aures
 * @license    https://www.gnu.org/licenses/agpl-3.0.en.html AGPL3
 * @link       https://castopod.org/
 */

return [
    'feed' => 'Flux RSS Podcast',
    'season' => 'Sezonul {seasonNumber}',
    'list_of_episodes_year' => '{year} episoade ({episodeCount})',
    'list_of_episodes_season' =>
        'Sezonul {seasonNumber} episoadele ({episodeCount})',
    'no_episode' => 'Nici un episod găsit!',
    'follow' => 'Urmăriţi',
    'followTitle' => 'Urmărește pe {actorDisplayName} în fediverse!',
    'followers' => '{numberOfFollowers, plural,
        one {# follower}
        few {# followeri}
        other {# followeri}
    }',
    'posts' => '{numberOfPosts, plural,
        one {# postare}
        few {# postări}
        other {# postări}
    }',
    'links' => 'Links',
    'activity' => 'Activitate',
    'episodes' => 'Episoade',
    'episodes_title' => 'Episoadele podcastului {podcastTitle}',
    'about' => 'Despre noi',
    'stats' => [
        'title' => 'Statistici',
        'number_of_seasons' => '{0, plural,
        one {# sezon}
        few {# sezoane}
        other {# sezoane}
    }',
        'number_of_episodes' => '{0, plural,
        one {# episod}
        few {# episoade}
        other {# episoade}
    }',
        'first_published_at' => 'Primul episod publicat pe {0, date, medium}',
    ],
    'sponsor' => 'Sponsor',
    'funding_links' => 'Link-uri de finanțare pentru {podcastTitle}',
    'find_on' => 'Găsește {podcastTitle} pe',
    'listen_on' => 'Ascultă pe',
    'persons' => '{personsCount, plural,
        one {# persoană}
        few {# persoane}
        other {# persoane}
    }',
    'persons_list' => 'Persoane',
    'castopod_website' => 'Castopod (website)',
];
