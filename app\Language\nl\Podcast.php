<?php

declare(strict_types=1);

/**
 * @copyright  2020 Ad Aures
 * @license    https://www.gnu.org/licenses/agpl-3.0.en.html AGPL3
 * @link       https://castopod.org/
 */

return [
    'feed' => 'RSS Podcast feed',
    'season' => 'Seizoen {seasonNumber}',
    'list_of_episodes_year' => '{year} afleveringen ({episodeCount})',
    'list_of_episodes_season' =>
        'Seizoen {seasonNumber} afleveringen ({episodeCount})',
    'no_episode' => 'Er zijn geen afleveringen gevonden!',
    'follow' => 'Abonneer',
    'followTitle' => 'Abonneer op {actorDisplayName} via de fediverse!',
    'followers' => '{numberOfFollowers, plural,
        one {# volger}
        other {# volgers}
    }',
    'posts' => '{numberOfPosts, plural,
        one {# bericht}
        other {# berichten}
    }',
    'links' => 'Links',
    'activity' => 'Activiteit',
    'episodes' => 'Afleveringen',
    'episodes_title' => 'Afleveringen van {podcastTitle}',
    'about' => 'Over Ons',
    'stats' => [
        'title' => 'Statistieken',
        'number_of_seasons' => '{0, plural,
            one {# seizoen}
            other {# seizoenen}
        }',
        'number_of_episodes' => '{0, plural,
            one {# aflevering}
            other {# afleveringen}
        }',
        'first_published_at' => 'Eerste aflevering gepubliceerd op {0, date, medium}',
    ],
    'sponsor' => 'Sponsor',
    'funding_links' => 'Financiering links voor {podcastTitle}',
    'find_on' => 'Vind {podcastTitle} op',
    'listen_on' => 'Luister op',
    'persons' => '{personsCount, plural,
        one {# persoon}
        other {# personen}
    }',
    'persons_list' => 'Personen',
    'castopod_website' => 'Castopod (website)',
];
