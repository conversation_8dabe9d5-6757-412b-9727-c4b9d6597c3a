<?php

declare(strict_types=1);

/**
 * @copyright  2020 Ad Aures
 * @license    https://www.gnu.org/licenses/agpl-3.0.en.html AGPL3
 * @link       https://castopod.org/
 */

return [
    'feed' => 'RSS Podcast feed',
    'season' => 'Musim {seasonNumber}',
    'list_of_episodes_year' => '{year} episodes ({episodeCount})',
    'list_of_episodes_season' =>
        'Episode-episode pada musim {seasonNumber} ({episodeCount})',
    'no_episode' => 'Tak ditemukan episode!',
    'follow' => 'Ikuti',
    'followTitle' => 'I<PERSON>ti {actorDisplayName} di fediverse!',
    'followers' => '{numberOfFollowers, plural,
        other {# pengikut}
    }',
    'posts' => '{numberOfPosts, plural,
        other {# postingan}
    }',
    'links' => 'Links',
    'activity' => 'Aktivitas',
    'episodes' => 'Episode',
    'episodes_title' => 'Episode-episode {podcastTitle}',
    'about' => 'Tentang',
    'stats' => [
        'title' => 'Statistik',
        'number_of_seasons' => '{0, plural,
            other {# musim}
        }',
        'number_of_episodes' => '{0, plural,
            other {# episode}
        }',
        'first_published_at' => 'Episode pertama diterbitkan pada {0, date, medium}',
    ],
    'sponsor' => 'Sponsor',
    'funding_links' => 'Funding links for {podcastTitle}',
    'find_on' => 'Cari {podcastTitle} di',
    'listen_on' => 'Listen on',
    'persons' => '{personsCount, plural,
        other {# orang}
    }',
    'persons_list' => 'Orang',
    'castopod_website' => 'Castopod (website)',
];
