---
title: <PERSON><PERSON><PERSON><PERSON> Docker slike
---

Castopod gura 3 Docker slike u Docker Hub tokom njegovog automatizovanog procesa
izrade:

- [**`castopod/castopod`**](https://hub.docker.com/r/castopod/castopod): sve u
  jednoj castopod slici koristeći ngink jedinicu
- [**`castopod/app`**](https://hub.docker.com/r/castopod/app): paket aplikacija
  sa svim Castopod zavisnostima
- [**`castopod/web-server`**](https://hub.docker.com/r/castopod/web-server):
  Nginx konfiguracija za Castopod

Additionally, Castopod requires a MySQL-compatible database. A Redis database
can be added as a cache handler.

## Podržani tagovi

- `develop` [nestabilno], najnoviji razvojni ogranak
- `beta` [stabilno], poslednja beta verzija
- `latest` [stabilno], poslednja verzija
- `1.x.x` [stabilno], specifi<PERSON>na verzija (od `1.0.0`)

## Primer upotrebe

1. Instalirajte [docker](https://docs.docker.com/get-docker/) i
   [docker-compose](https://docs.docker.com/compose/install/)

2. Napravite `docker-compose.yml` datoteku sa sledećim:

   ```yml
   version: "3.7"

   services:
     app:
       image: castopod/castopod:latest
       container_name: "castopod-app"
       volumes:
         - castopod-media:/var/www/castopod/public/media
       environment:
         MYSQL_DATABASE: castopod
         MYSQL_USER: castopod
         MYSQL_PASSWORD: changeme
         CP_BASEURL: "https://castopod.example.com"
         CP_ANALYTICS_SALT: changeme
         CP_CACHE_HANDLER: redis
         CP_REDIS_HOST: redis
       networks:
         - castopod-app
         - castopod-db
       ports:
         - 8000:8000
       restart: unless-stopped

     mariadb:
       image: mariadb:10.5
       container_name: "castopod-mariadb"
       networks:
         - castopod-db
       volumes:
         - castopod-db:/var/lib/mysql
       environment:
         MYSQL_ROOT_PASSWORD: changeme
         MYSQL_DATABASE: castopod
         MYSQL_USER: castopod
         MYSQL_PASSWORD: changeme
       restart: unless-stopped

     redis:
       image: redis:7.0-alpine
       container_name: "castopod-redis"
       volumes:
         - castopod-cache:/data
       networks:
         - castopod-app

   volumes:
     castopod-media:
     castopod-db:
     castopod-cache:

   networks:
     castopod-app:
     castopod-db:
   ```

   Morate prilagoditi neke varijable svojim potrebama (npr. `CP_BASEURL`,
   `MYSQL_ROOT_PASSWORD`, `MYSQL_PASSWORD` and `CP_ANALYTICS_SALT`).

3. Podesite obrnuti proksi za TLS (SSL/HTTPS)

   TLS is mandatory for ActivityPub to work. This job can easily be handled by a
   reverse proxy, for example with [Caddy](https://caddyserver.com/):

   ```
   #castopod
   castopod.example.com {
       reverse_proxy localhost:8000
   }
   ```

4. Pokrenite `docker-compose up -d`, sačekajte da se pokrene i idite na
   `https://castopod.example.com/cp-install` da biste završili instalaciju
   Castopod-a!

5. You're all set, start podcasting! 🎙️🚀

## Promenljive okruženja

- **castopod/castopod** i **castopod/app**

  | Naziv promenljive                     | Vrsta (`uobičajeno`)         | Podrazumevano    |
  | ------------------------------------- | ---------------------------- | ---------------- |
  | **`CP_BASEURL`**                      | string                       | `nedefinisano`   |
  | **`CP_MEDIA_BASEURL`**                | ?string                      | `CP_BASEURL`     |
  | **`CP_ADMIN_GATEWAY`**                | ?string                      | `"cp-admin"`     |
  | **`CP_AUTH_GATEWAY`**                 | ?string                      | `"cp-auth"`      |
  | **`CP_ANALYTICS_SALT`**               | string                       | `nedefinisano`   |
  | **`CP_DATABASE_HOSTNAME`**            | ?string                      | `"mariadb"`      |
  | **`CP_DATABASE_NAME`**                | ?string                      | `MYSQL_DATABASE` |
  | **`CP_DATABASE_USERNAME`**            | ?string                      | `MYSQL_USER`     |
  | **`CP_DATABASE_PASSWORD`**            | ?string                      | `MYSQL_PASSWORD` |
  | **`CP_DATABASE_PREFIX`**              | ?string                      | `"cp_"`          |
  | **`CP_CACHE_HANDLER`**                | [`"datoteka"` ili `"redis"`] | `"file"`         |
  | **`CP_REDIS_HOST`**                   | ?string                      | `"localhost"`    |
  | **`CP_REDIS_PASSWORD`**               | ?string                      | `null`           |
  | **`CP_REDIS_PORT`**                   | ?number                      | `6379`           |
  | **`CP_REDIS_DATABASE`**               | ?number                      | `0`              |
  | **`CP_EMAIL_SMTP_HOST`**              | ?string                      | `nedefinisano`   |
  | **`CP_EMAIL_FROM`**                   | ?string                      | `nedefinisano`   |
  | **`CP_EMAIL_SMTP_USERNAME`**          | ?string                      | `"localhost"`    |
  | **`CP_EMAIL_SMTP_PASSWORD`**          | ?string                      | `null`           |
  | **`CP_EMAIL_SMTP_PORT`**              | ?number                      | `25`             |
  | **`CP_EMAIL_SMTP_CRYPTO`**            | [`"tls"` ili `"ssl"`]        | `"tls"`          |
  | **`CP_ENABLE_2FA`**                   | ?boolean                     | `nedefinisano`   |
  | **`CP_MEDIA_FILE_MANAGER`**           | ?string                      | `nedefinisano`   |
  | **`CP_MEDIA_S3_ENDPOINT`**            | ?string                      | `nedefinisano`   |
  | **`CP_MEDIA_S3_KEY`**                 | ?string                      | `nedefinisano`   |
  | **`CP_MEDIA_S3_SECRET`**              | ?string                      | `nedefinisano`   |
  | **`CP_MEDIA_S3_REGION`**              | ?string                      | `nedefinisano`   |
  | **`CP_MEDIA_S3_BUCKET`**              | ?string                      | `nedefinisano`   |
  | **`CP_MEDIA_S3_PROTOCOL`**            | ?number                      | `nedefinisano`   |
  | **`CP_MEDIA_S3_PATH_STYLE_ENDPOINT`** | ?boolean                     | `nedefinisano`   |
  | **`CP_MEDIA_S3_KEY_PREFIX`**          | ?string                      | `nedefinisano`   |
  | **`CP_DISABLE_HTTPS`**                | ?[`0` ili `1`]               | `nedefinisano`   |
  | **`CP_MAX_BODY_SIZE`**                | ?number (sa sufiksom)        | `512M`           |
  | **`CP_PHP_MEMORY_LIMIT`**             | ?number (sa sufiksom)        | `512M`           |
  | **`CP_TIMEOUT`**                      | ?number                      | `900`            |

- **castopod/veb server**

  | Naziv promenljive      | Vrsta                 | Podrazumevano |
  | ---------------------- | --------------------- | ------------- |
  | **`CP_APP_HOSTNAME`**  | ?string               | `"app"`       |
  | **`CP_MAX_BODY_SIZE`** | ?number (sa sufiksom) | `512M`        |
  | **`CP_TIMEOUT`**       | ?number               | `900`         |
