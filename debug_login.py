#!/usr/bin/env python3
import requests
import re

def extract_csrf_token(html):
    """Extract CSRF token from HTML"""
    patterns = [
        r'name="csrf_test_name"[^>]*value="([^"]+)"',
        r'value="([^"]+)"[^>]*name="csrf_test_name"',
        r'csrf_test_name[^>]*value="([^"]+)"'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, html)
        if match:
            return match.group(1)
    return None

def debug_registration_and_login():
    base_url = "http://localhost:8000"
    session = requests.Session()
    
    print("=== DEBUGGING REGISTRATION AND LOGIN ===\n")
    
    # Step 1: Test basic connectivity
    print("1. Testing basic connectivity...")
    try:
        resp = session.get(base_url, timeout=10)
        print(f"   ✓ Base URL accessible: {resp.status_code}")
    except Exception as e:
        print(f"   ✗ Cannot connect to {base_url}: {e}")
        return
    
    # Step 2: Check if registration is available
    print("\n2. Checking registration page...")
    try:
        resp = session.get(f"{base_url}/register", timeout=10)
        print(f"   Status: {resp.status_code}")
        print(f"   URL: {resp.url}")
        
        if resp.status_code == 404:
            print("   ✗ Registration not available (404)")
            return
        elif resp.status_code != 200:
            print(f"   ✗ Unexpected status: {resp.status_code}")
            return
        
        # Check for registration form
        if "register" in resp.text.lower() and "password" in resp.text.lower():
            print("   ✓ Registration form found")
        else:
            print("   ✗ Registration form not found")
            print(f"   Page content preview: {resp.text[:200]}...")
            
        csrf_token = extract_csrf_token(resp.text)
        if csrf_token:
            print(f"   ✓ CSRF token found: {csrf_token[:20]}...")
        else:
            print("   ✗ CSRF token not found")
            # Show form fields for debugging
            form_fields = re.findall(r'<input[^>]*name="([^"]+)"', resp.text)
            print(f"   Form fields found: {form_fields}")
            
    except Exception as e:
        print(f"   ✗ Error accessing registration: {e}")
        return
    
    # Step 3: Attempt registration
    print("\n3. Attempting registration...")
    try:
        reg_data = {
            'username': 'debuguser',
            'email': '<EMAIL>',
            'password': 'Password123!',
            'password_confirm': 'Password123!'
        }
        
        if csrf_token:
            reg_data['csrf_test_name'] = csrf_token
        
        resp = session.post(f"{base_url}/register", data=reg_data, allow_redirects=False, timeout=10)
        print(f"   Status: {resp.status_code}")
        print(f"   Headers: {dict(resp.headers)}")
        
        if resp.status_code in [302, 303]:
            print("   ✓ Registration successful (redirect)")
            redirect_url = resp.headers.get('Location', '')
            print(f"   Redirect to: {redirect_url}")
        elif resp.status_code == 200:
            print("   ? Registration returned 200 (check for errors)")
            if "error" in resp.text.lower():
                print("   ✗ Registration errors found in response")
                # Extract error messages
                errors = re.findall(r'error[^>]*>([^<]+)', resp.text, re.IGNORECASE)
                for error in errors:
                    print(f"     Error: {error.strip()}")
            else:
                print("   ? No obvious errors in response")
        else:
            print(f"   ✗ Registration failed: {resp.status_code}")
            print(f"   Response preview: {resp.text[:300]}...")
            
    except Exception as e:
        print(f"   ✗ Registration error: {e}")
        return
    
    # Step 4: Test login page
    print("\n4. Checking login page...")
    try:
        resp = session.get(f"{base_url}/login", timeout=10)
        print(f"   Status: {resp.status_code}")
        print(f"   URL: {resp.url}")
        
        if resp.status_code != 200:
            print(f"   ✗ Cannot access login page: {resp.status_code}")
            return
            
        if "login" in resp.text.lower() and "password" in resp.text.lower():
            print("   ✓ Login form found")
        else:
            print("   ✗ Login form not found")
            
        csrf_token = extract_csrf_token(resp.text)
        if csrf_token:
            print(f"   ✓ CSRF token found: {csrf_token[:20]}...")
        else:
            print("   ✗ CSRF token not found")
            
    except Exception as e:
        print(f"   ✗ Error accessing login: {e}")
        return
    
    # Step 5: Attempt login
    print("\n5. Attempting login...")
    try:
        login_data = {
            'email': '<EMAIL>',
            'password': 'Password123!'
        }
        
        if csrf_token:
            login_data['csrf_test_name'] = csrf_token
        
        resp = session.post(f"{base_url}/login", data=login_data, allow_redirects=False, timeout=10)
        print(f"   Status: {resp.status_code}")
        print(f"   Headers: {dict(resp.headers)}")
        
        if resp.status_code in [302, 303]:
            print("   ✓ Login successful (redirect)")
            redirect_url = resp.headers.get('Location', '')
            print(f"   Redirect to: {redirect_url}")
            
            # Test if we can access admin area
            print("\n6. Testing admin access...")
            try:
                admin_resp = session.get(f"{base_url}/cp-admin", timeout=10)
                print(f"   Admin status: {admin_resp.status_code}")
                print(f"   Admin URL: {admin_resp.url}")
                
                if admin_resp.status_code == 200:
                    print("   ✓ Admin area accessible")
                    
                    # Look for interact-as-actor form
                    if "interact-as-actor" in admin_resp.text:
                        print("   ✓ Found interact-as-actor functionality")
                    else:
                        print("   ? interact-as-actor not found in admin page")
                        
                else:
                    print(f"   ✗ Cannot access admin area: {admin_resp.status_code}")
                    
            except Exception as e:
                print(f"   ✗ Admin access error: {e}")
                
        elif resp.status_code == 200:
            print("   ? Login returned 200 (check for errors)")
            if "error" in resp.text.lower() or "invalid" in resp.text.lower():
                print("   ✗ Login errors found")
                errors = re.findall(r'(error|invalid)[^>]*>([^<]+)', resp.text, re.IGNORECASE)
                for error in errors:
                    print(f"     Error: {error[1].strip()}")
            else:
                print("   ? No obvious errors, but no redirect")
        else:
            print(f"   ✗ Login failed: {resp.status_code}")
            print(f"   Response preview: {resp.text[:300]}...")
            
    except Exception as e:
        print(f"   ✗ Login error: {e}")

if __name__ == "__main__":
    debug_registration_and_login()
