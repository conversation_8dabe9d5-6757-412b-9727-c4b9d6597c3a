<?php

declare(strict_types=1);

/**
 * @copyright  2020 Ad Aures
 * @license    https://www.gnu.org/licenses/agpl-3.0.en.html AGPL3
 * @link       https://castopod.org/
 */

return [
    'feed' => 'RSS podcast feed',
    'season' => '<PERSON><PERSON><PERSON> {seasonNumber}',
    'list_of_episodes_year' => '{year} episoder ({episodeCount})',
    'list_of_episodes_season' =>
        '<PERSON><PERSON><PERSON> {seasonNumber} episoder ({episodeCount})',
    'no_episode' => 'Ingen afsnit fundet!',
    'follow' => 'Følg',
    'followTitle' => 'Følg {actorDisplayName} i fediverset!',
    'followers' => '{numberOfFollowers, plural,
        one {# følger}
        other {# føgere}
    }',
    'posts' => '{numberOfPosts, plural,
        one {# indlæg}
        other {# indlæg}
    }',
    'links' => 'Links',
    'activity' => 'Aktivitet',
    'episodes' => 'Episoder',
    'episodes_title' => 'Episoder af {podcastTitle}',
    'about' => 'Om',
    'stats' => [
        'title' => 'Statistikker',
        'number_of_seasons' => '{0, plural,
        one {# sæson}
        other {# sæsoner}
    }',
        'number_of_episodes' => '{0, plural,
            one {# episode}
            other {# episoder}
        }',
        'first_published_at' => 'Første episode offentliggjort den {0, date, medium}',
    ],
    'sponsor' => 'Sponsor',
    'funding_links' => 'Finansieringslinks til {podcastTitle}',
    'find_on' => 'Find {podcastTitle} på',
    'listen_on' => 'Lyt på',
    'persons' => '{personsCount, plural,
        one {# person}
        other {# personer}
    }',
    'persons_list' => 'Personer',
    'castopod_website' => 'Castopod (website)',
];
