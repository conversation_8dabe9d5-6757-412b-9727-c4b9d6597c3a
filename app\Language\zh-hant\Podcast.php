<?php

declare(strict_types=1);

/**
 * @copyright  2020 Ad Aures
 * @license    https://www.gnu.org/licenses/agpl-3.0.en.html AGPL3
 * @link       https://castopod.org/
 */

return [
    'feed' => '播客 RSS 摘要',
    'season' => '第 {seasonNumber} 季',
    'list_of_episodes_year' => '{year} 集數 ({episodeCount})',
    'list_of_episodes_season' =>
        '第 {seasonNumber} 季(第 {episodeCount} 集)',
    'no_episode' => '没有找到節目！',
    'follow' => '追蹤',
    'followTitle' => '在聯邦宇宙中追蹤 {actorDisplayName} ！',
    'followers' => '{numberOfFollowers, plural,
        one {# 追蹤者}
        other {# 追蹤者}
    }',
    'posts' => '{numberOfPosts, plural,
        one {# 貼文}
        other {# 貼文}
    }}',
    'links' => '連結',
    'activity' => '活動',
    'episodes' => '劇集',
    'episodes_title' => '{podcastTitle} 的劇集',
    'about' => '關於',
    'stats' => [
        'title' => '統計',
        'number_of_seasons' => '{0, plural,
            one {# 季}
            other {# 季}
        }',
        'number_of_episodes' => '{0, plural,
            one {# 劇集}
            other {# 劇集}
        }',
        'first_published_at' => '首集發佈於 {0, date, medium}',
    ],
    'sponsor' => '贊助者',
    'funding_links' => '{podcastTitle} 的贊助連結',
    'find_on' => '查找 {podcastTitle} 於',
    'listen_on' => '收聽',
    'persons' => '{personsCount, plural,
        one {# 人}
        other {# 人}
    }',
    'persons_list' => '人物',
    'castopod_website' => 'Castopod (網站)',
];
