<?php

declare(strict_types=1);

/**
 * @copyright  2020 Ad Aures
 * @license    https://www.gnu.org/licenses/agpl-3.0.en.html AGPL3
 * @link       https://castopod.org/
 */

return [
    'feed' => 'RSS Podcast flöde',
    'season' => 'Säsong {seasonNumber}',
    'list_of_episodes_year' => '{year} avsnitt ({episodeCount})',
    'list_of_episodes_season' =>
        'Säsong {seasonNumber} avsnitt ({episodeCount})',
    'no_episode' => 'Inga avsnitt hittades!',
    'follow' => 'Följ',
    'followTitle' => 'Följ {actorDisplayName} på fediverse!',
    'followers' => '{numberOfFollowers, plural,
        one {# följare}
        other {# följare}
    }',
    'posts' => '{numberOfPosts, plural,
        one {# inlägg}
        other {# inlägg}
    }',
    'links' => 'Länkar',
    'activity' => 'Aktivitet',
    'episodes' => 'Avsnitt',
    'episodes_title' => 'Avsnitt av {podcastTitle}',
    'about' => 'Om',
    'stats' => [
        'title' => 'Statistik',
        'number_of_seasons' => '{0, plural,
            one {# säsong}
            other {# säsonger}
        }',
        'number_of_episodes' => '{0, plural,
            one {# avsnitt}
            other {# avsnitt}
        }',
        'first_published_at' => 'Första avsnittet publicerades den {0, date, medium}',
    ],
    'sponsor' => 'Sponsor',
    'funding_links' => 'Finansiera länkar för {podcastTitle}',
    'find_on' => 'Hitta {podcastTitle} på',
    'listen_on' => 'Lyssna på',
    'persons' => '{personsCount, plural,
        one {# person}
        other {# personer}
    }',
    'persons_list' => 'Personer',
    'castopod_website' => 'Castopod (website)',
];
