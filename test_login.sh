#!/bin/bash

BASE_URL="http://localhost:8000"
COOKIE_JAR="test_cookies.txt"

echo "=== TESTING CASTOPOD LOGIN ==="
echo "Base URL: $BASE_URL"
echo

# Clean up any existing cookies
rm -f $COOKIE_JAR

# Test 1: Basic connectivity
echo "1. Testing basic connectivity..."
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL")
if [ "$HTTP_CODE" = "200" ]; then
    echo "   ✓ Base URL accessible ($HTTP_CODE)"
else
    echo "   ✗ Cannot connect to $BASE_URL (HTTP $HTTP_CODE)"
    exit 1
fi

# Test 2: Check registration page
echo
echo "2. Checking registration page..."
curl -s -c "$COOKIE_JAR" "$BASE_URL/register" > reg_page.html
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" -c "$COOKIE_JAR" "$BASE_URL/register")

if [ "$HTTP_CODE" = "200" ]; then
    echo "   ✓ Registration page accessible ($HTTP_CODE)"
    
    # Extract CSRF token
    CSRF_TOKEN=$(grep -o 'name="csrf_test_name"[^>]*value="[^"]*"' reg_page.html | sed 's/.*value="\([^"]*\)".*/\1/')
    
    if [ -n "$CSRF_TOKEN" ]; then
        echo "   ✓ CSRF token found: ${CSRF_TOKEN:0:20}..."
    else
        echo "   ✗ CSRF token not found"
        echo "   Form fields in page:"
        grep -o 'name="[^"]*"' reg_page.html | sort | uniq
    fi
else
    echo "   ✗ Registration page not accessible (HTTP $HTTP_CODE)"
    exit 1
fi

# Test 3: Register user
echo
echo "3. Attempting registration..."
REGISTER_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
    -b "$COOKIE_JAR" -c "$COOKIE_JAR" \
    -X POST "$BASE_URL/register" \
    -d "username=testuser123" \
    -d "email=<EMAIL>" \
    -d "password=Password123!" \
    -d "password_confirm=Password123!" \
    -d "csrf_test_name=$CSRF_TOKEN")

HTTP_CODE=$(echo "$REGISTER_RESPONSE" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
RESPONSE_BODY=$(echo "$REGISTER_RESPONSE" | sed 's/HTTPSTATUS:[0-9]*$//')

echo "   Registration HTTP Code: $HTTP_CODE"

if [ "$HTTP_CODE" = "302" ] || [ "$HTTP_CODE" = "303" ]; then
    echo "   ✓ Registration successful (redirect)"
elif [ "$HTTP_CODE" = "200" ]; then
    echo "   ? Registration returned 200, checking for errors..."
    if echo "$RESPONSE_BODY" | grep -qi "error\|invalid"; then
        echo "   ✗ Registration errors found:"
        echo "$RESPONSE_BODY" | grep -i "error\|invalid" | head -3
    else
        echo "   ? No obvious errors in response"
    fi
else
    echo "   ✗ Registration failed (HTTP $HTTP_CODE)"
    echo "   Response preview:"
    echo "$RESPONSE_BODY" | head -5
fi

# Test 4: Get login page
echo
echo "4. Checking login page..."
curl -s -b "$COOKIE_JAR" -c "$COOKIE_JAR" "$BASE_URL/login" > login_page.html
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" -b "$COOKIE_JAR" "$BASE_URL/login")

if [ "$HTTP_CODE" = "200" ]; then
    echo "   ✓ Login page accessible ($HTTP_CODE)"
    
    # Extract new CSRF token
    CSRF_TOKEN=$(grep -o 'name="csrf_test_name"[^>]*value="[^"]*"' login_page.html | sed 's/.*value="\([^"]*\)".*/\1/')
    
    if [ -n "$CSRF_TOKEN" ]; then
        echo "   ✓ CSRF token found: ${CSRF_TOKEN:0:20}..."
    else
        echo "   ✗ CSRF token not found"
    fi
else
    echo "   ✗ Login page not accessible (HTTP $HTTP_CODE)"
    exit 1
fi

# Test 5: Attempt login
echo
echo "5. Attempting login..."
LOGIN_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
    -b "$COOKIE_JAR" -c "$COOKIE_JAR" \
    -X POST "$BASE_URL/login" \
    -d "email=<EMAIL>" \
    -d "password=Password123!" \
    -d "csrf_test_name=$CSRF_TOKEN")

HTTP_CODE=$(echo "$LOGIN_RESPONSE" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
RESPONSE_BODY=$(echo "$LOGIN_RESPONSE" | sed 's/HTTPSTATUS:[0-9]*$//')

echo "   Login HTTP Code: $HTTP_CODE"

if [ "$HTTP_CODE" = "302" ] || [ "$HTTP_CODE" = "303" ]; then
    echo "   ✓ Login successful (redirect)"
    
    # Test admin access
    echo
    echo "6. Testing admin access..."
    ADMIN_CODE=$(curl -s -o /dev/null -w "%{http_code}" -b "$COOKIE_JAR" "$BASE_URL/cp-admin")
    echo "   Admin access HTTP Code: $ADMIN_CODE"
    
    if [ "$ADMIN_CODE" = "200" ]; then
        echo "   ✓ Admin area accessible"
        
        # Check for vulnerability
        curl -s -b "$COOKIE_JAR" "$BASE_URL/cp-admin" > admin_page.html
        if grep -q "interact-as-actor" admin_page.html; then
            echo "   ✓ Found interact-as-actor functionality - VULNERABLE!"
        else
            echo "   ? interact-as-actor not found"
        fi
    else
        echo "   ✗ Cannot access admin area (HTTP $ADMIN_CODE)"
    fi
    
elif [ "$HTTP_CODE" = "200" ]; then
    echo "   ? Login returned 200, checking for errors..."
    if echo "$RESPONSE_BODY" | grep -qi "error\|invalid\|incorrect"; then
        echo "   ✗ Login errors found:"
        echo "$RESPONSE_BODY" | grep -i "error\|invalid\|incorrect" | head -3
    else
        echo "   ? No obvious errors, but no redirect"
        echo "   Response preview:"
        echo "$RESPONSE_BODY" | head -5
    fi
else
    echo "   ✗ Login failed (HTTP $HTTP_CODE)"
    echo "   Response preview:"
    echo "$RESPONSE_BODY" | head -5
fi

# Cleanup
echo
echo "Cleaning up temporary files..."
rm -f reg_page.html login_page.html admin_page.html $COOKIE_JAR

echo
echo "=== TEST COMPLETE ==="
