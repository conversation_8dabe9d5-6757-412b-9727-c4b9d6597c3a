<?php

declare(strict_types=1);

/**
 * @copyright  2020 Ad Aures
 * @license    https://www.gnu.org/licenses/agpl-3.0.en.html AGPL3
 * @link       https://castopod.org/
 */

return [
    'feed' => 'RSS Podcast feed',
    'season' => 'الموسم {seasonNumber}',
    'list_of_episodes_year' => 'حلَقات {year} ({episodeCount})',
    'list_of_episodes_season' =>
        'الموسم {seasonNumber} الحلقات ({episodeCount})',
    'no_episode' => 'لم يتم العثور على أية حلقة!',
    'follow' => 'متابعة',
    'followTitle' => 'تابع {actorDisplayName} على الفديفرس!',
    'followers' => '{numberOfFollowers, plural,
        one {# follower}
        other {# followers}
    }',
    'posts' => '{numberOfPosts, plural,
        one {# post}
        other {# posts}
    }',
    'links' => 'Links',
    'activity' => 'النشاط',
    'episodes' => 'الحلقات',
    'episodes_title' => 'حلقات {podcastTitle}',
    'about' => 'عن',
    'stats' => [
        'title' => 'الإحصائيات',
        'number_of_seasons' => '{0, plural,
            one {# season}
            other {# seasons}
        }',
        'number_of_episodes' => '{0, plural,
            one {# episode}
            other {# episodes}
        }',
        'first_published_at' => 'First episode published on {0, date, medium}',
    ],
    'sponsor' => 'الراعي',
    'funding_links' => 'Funding links for {podcastTitle}',
    'find_on' => 'Find {podcastTitle} on',
    'listen_on' => 'Listen on',
    'persons' => '{personsCount, plural,
        one {# person}
        other {# persons}
    }',
    'persons_list' => 'أشخاص',
    'castopod_website' => 'Castopod (website)',
];
