---
title: manifest.json reference
---

This page details the attributes of a Castopod Plugin's manifest, which must be
a JSON file.

### name (required)

The plugin name, including 'vendor-name/' prefix. Examples:

- acme/hello-world
- adaures/click

The name must be lowercase and consist of words separated by `-`, `.` or `_`.
The complete name should match
`^[a-z0-9]([_.-]?[a-z0-9]+)*\/[a-z0-9]([_.-]?[a-z0-9]+)*$`.

### version (required)

The plugin's semantic version (eg. 1.0.0) - see https://semver.org/

### description

The plugin's description. This helps people discover your plugin when listed in
repositories.

### authors

Array one or more persons having authored the plugin. A person is an object with
a required "name" field and optional "email" and "url" fields:

```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "url": "https://example.com/"
}
```

Or you can shorten the object into a single string:

```json
"<PERSON> <<EMAIL>> (https://example.com/)"
```

### homepage

The URL to the project homepage.

### license

**Default:** `"UNLICENSED"`

Specify a license for your plugin so that people know how they are permitted to
use it, and any restrictions you're placing on it.

### private

**Default:** `false`

Whether or not to publish the plugin in public directories. If set to `true`,
directories should refuse to publish the plugin.

### submodule

**Default:** `false`

Indicates whether the plugin is part of a monorepo (a single Git repository
containing multiple plugins). If `true`, the plugin shares its repository with
other plugins. In this case, releases should be tagged using the format
`<plugin-name>@<version>` (eg. `acme/hello-world@1.0.0`) to ensure proper
version indexing by plugin repositories.

### keywords

Array of strings to help your plugin get discovered when listed in repositories.

### minCastopodVersion

The minimal version of Castopod with which the plugin is compatible.

### hooks

List of hooks used by the plugin. If the hook is not specified, Castopod will
not run it.

### settings

Declare settings forms for persisting user data. The plugin's settings forms can
be declared at three levels: `general`, `podcast`, and `episode`.

Each level accepts one or more fields, identified by a key.

```json
{
  "settings": {
    "general": { // general settings form
      "field-key": {
        "type": "text", // default field type: a text input
        "label": "Enter a text"
      },
      …
    },
    "podcast": {…}, // settings form for each podcast
    "episode": {…}, // settings form for each episode
  }
}
```

The `general`, `podcast`, and `episode` settings are of `Fields` object with
each property being a field key and the value being a `Field` object.

#### Field object

A field is a form element:

| Property           | Type                                                                                                                                                            | Note                                                                  |
| ------------------ | --------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------- |
| `type`             | `checkbox`, `datetime`, `email`, `group`, `html`, `markdown`, `number`, `radio-group`, `rss`, `select-multiple`, `select`, `text`, `textarea`, `toggler`, `url` | Default is `text`                                                     |
| `label` (required) | `string`                                                                                                                                                        | Can be translated (see i18n)                                          |
| `hint`             | `string`                                                                                                                                                        | Can be translated (see i18n)                                          |
| `helper`           | `string`                                                                                                                                                        | Can be translated (see i18n)                                          |
| `defaultValue`     | `string`                                                                                                                                                        | You can specify multiple comma separated values for `select-multiple` |
| `validationRules`  | `string` \| `array`                                                                                                                                             | See [available validation rules](#available-validation-rules)         |
| `optional`         | `boolean`                                                                                                                                                       | Default is `false`                                                    |
| `options`          | `Options`                                                                                                                                                       | Required for `radio-group`, `select-multiple`, and `select` types.    |
| `multiple`         | `boolean`                                                                                                                                                       | Default is `false`                                                    |
| `fields`           | `Array<string, Field>`                                                                                                                                          | Required for `group` type                                             |

#### Options object

The `Options` object properties are option keys and the value is an `Option`.

##### Option object

| Property           | Type     | Note                         |
| ------------------ | -------- | ---------------------------- |
| `label` (required) | `string` | Can be translated (see i18n) |
| `description`      | `string` | Can be translated (see i18n) |

### files

Array of file patterns that describes the entries to be included when your
plugin is installed.

### repository

Repository where the plugin's code lives. Helpful for people who want to
contribute.

#### Available validation rules

The following rules are a subset of
[CodeIgniter4's validation rules](https://codeigniter.com/user_guide/libraries/validation.html#available-rules).

| Rule                  | Parameter | Description                                                                                                                                                                                                                                                                                                                      | Example                                     |
| --------------------- | --------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------- |
| alpha                 | No        | Fails if field has anything other than alphabetic characters in ASCII.                                                                                                                                                                                                                                                           |                                             |
| alpha_dash            | No        | Fails if field contains anything other than alphanumeric characters, underscores or dashes in ASCII.                                                                                                                                                                                                                             |                                             |
| alpha_numeric         | No        | Fails if field contains anything other than alphanumeric characters in ASCII.                                                                                                                                                                                                                                                    |                                             |
| alpha_numeric_punct   | No        | Fails if field contains anything other than alphanumeric, space, or this limited set of punctuation characters: `~` (tilde), `!` (exclamation), `#` (number), `$` (dollar), `%` (percent), `&` (ampersand), `*` (asterisk), `-` (dash), `_` (underscore), `+` (plus), `=` (equals), `\|` (vertical bar),`:`(colon),`.` (period). |                                             |
| alpha_numeric_space   | No        | Fails if field contains anything other than alphanumeric or space characters in ASCII.                                                                                                                                                                                                                                           |                                             |
| alpha_space           | No        | Fails if field contains anything other than alphabetic characters or spaces in ASCII.                                                                                                                                                                                                                                            |                                             |
| decimal               | No        | Fails if field contains anything other than a decimal number. Also accepts a `+` or `-` sign for the number.                                                                                                                                                                                                                     |                                             |
| differs               | Yes       | Fails if field does not differ from the one in the parameter.                                                                                                                                                                                                                                                                    | `differs[field_name]`                       |
| exact_length          | Yes       | Fails if field length is not exactly the parameter value. One or more comma-separated values are possible.                                                                                                                                                                                                                       | `exact_length[5]` or `exact_length[5,8,12]` |
| greater_than          | Yes       | Fails if field is less than or equal to the parameter value or not numeric.                                                                                                                                                                                                                                                      | `greater_than[8]`                           |
| greater_than_equal_to | Yes       | Fails if field is less than the parameter value, or not numeric.                                                                                                                                                                                                                                                                 | `greater_than_equal_to[5]`                  |
| hex                   | No        | Fails if field contains anything other than hexadecimal characters.                                                                                                                                                                                                                                                              |                                             |
| in_list               | Yes       | Fails if field is not within a predetermined list.                                                                                                                                                                                                                                                                               | `in_list[red,blue,green]`                   |
| integer               | No        | Fails if field contains anything other than an integer.                                                                                                                                                                                                                                                                          |                                             |
| is_natural            | No        | Fails if field contains anything other than a natural number: `0`, `1`, `2`, `3`, etc.                                                                                                                                                                                                                                           |                                             |
| is_natural_no_zero    | No        | Fails if field contains anything other than a natural number, except zero: `1`, `2`, `3`, etc.                                                                                                                                                                                                                                   |                                             |
| less_than             | Yes       | Fails if field is greater than or equal to the parameter value or not numeric.                                                                                                                                                                                                                                                   | `less_than[8]`                              |
| less_than_equal_to    | Yes       | Fails if field is greater than the parameter value or not numeric.                                                                                                                                                                                                                                                               | `less_than_equal_to[8]`                     |
| max_length            | Yes       | Fails if field is longer than the parameter value.                                                                                                                                                                                                                                                                               | `max_length[8]`                             |
| min_length            | Yes       | Fails if field is shorter than the parameter value.                                                                                                                                                                                                                                                                              | `min_length[3]`                             |
| not_in_list           | Yes       | Fails if field is within a predetermined list.                                                                                                                                                                                                                                                                                   | `not_in_list[red,blue,green]`               |
| regex_match           | Yes       | Fails if field does not match the regular expression.                                                                                                                                                                                                                                                                            | `regex_match[/regex/]`                      |
| valid_base64          | No        | Fails if field contains anything other than valid Base64 characters.                                                                                                                                                                                                                                                             |                                             |
| valid_date            | Yes       | Fails if field does not contain a valid date. Any string that `strtotime()` accepts is valid if you don't specify an optional parameter that matches a date format.                                                                                                                                                              | `valid_date[d/m/Y]`                         |
