<?php

declare(strict_types=1);

/**
 * @copyright  2020 Ad Aures
 * @license    https://www.gnu.org/licenses/agpl-3.0.en.html AGPL3
 * @link       https://castopod.org/
 */

return [
    'feed' => 'Fonte RSS do Podcast',
    'season' => 'Tempada {seasonNumber}',
    'list_of_episodes_year' => 'Episodios en {year} ({episodeCount})',
    'list_of_episodes_season' =>
        'Episodios da tempada {seasonNumber} ({episodeCount})',
    'no_episode' => 'Non se atoparon episodios!',
    'follow' => 'Seguir',
    'followTitle' => 'Segue a {actorDisplayName} no fediverso!',
    'followers' => '{numberOfFollowers, plural,
        one {# seguidora}
        other {# seguidoras}
    }',
    'posts' => '{numberOfPosts, plural,
        one {# publicación}
        other {# publicacións}
    }',
    'links' => 'Ligazóns',
    'activity' => 'Actividade',
    'episodes' => 'Episodios',
    'episodes_title' => 'Episodios de {podcastTitle}',
    'about' => 'Acerca de',
    'stats' => [
        'title' => 'Estatísticas',
        'number_of_seasons' => '{0, plural,
            one {# tempada}
            other {# tempadas}
        }',
        'number_of_episodes' => '{0, plural,
            one {# episodio}
            other {# episodios}
        }',
        'first_published_at' => 'Primeiro episodio publicado o {0, date, medium}',
    ],
    'sponsor' => 'Apoiar',
    'funding_links' => 'Ligazóns para apoiar a {podcastTitle}',
    'find_on' => 'Atopa a {podcastTitle} en',
    'listen_on' => 'Escoita en',
    'persons' => '{personsCount, plural,
        one {# persoa}
        other {# persoas}
    }',
    'persons_list' => 'Persoas',
    'castopod_website' => 'Castopod (sitio web)',
];
