<?php

declare(strict_types=1);

/**
 * @copyright  2020 Ad Aures
 * @license    https://www.gnu.org/licenses/agpl-3.0.en.html AGPL3
 * @link       https://castopod.org/
 */

return [
    'all_podcasts' => '全部播客',
    'sort_by' => '排序方式',
    'sort_options' => [
        'activity' => '近期活动',
        'created_desc' => '按时间顺序最新的优先',
        'created_asc' => '按时间顺序最旧的优先',
    ],
    'no_podcast' => '没有找到播客',
];
