<?php

declare(strict_types=1);

/**
 * @copyright  2020 Ad Aures
 * @license    https://www.gnu.org/licenses/agpl-3.0.en.html AGPL3
 * @link       https://castopod.org/
 */

return [
    'feed' => '播客 RSS 摘要',
    'season' => '第 {seasonNumber} 季',
    'list_of_episodes_year' => '{year} 剧集 ({episodeCount})',
    'list_of_episodes_season' =>
        '第 {seasonNumber} 季(第 {episodeCount} 集)',
    'no_episode' => '没有找到剧集！',
    'follow' => '关注',
    'followTitle' => '在联邦宇宙中关注 {actorDisplayName} ！',
    'followers' => '{numberOfFollowers, plural,
        one {# 关注者}
        other {# 关注者}
    }',
    'posts' => '{numberOfPosts, plural,
        one {# 帖子}
        other {# 帖子}
    }}',
    'links' => 'Links',
    'activity' => '活动',
    'episodes' => '剧集',
    'episodes_title' => '{podcastTitle} 的剧集',
    'about' => '关于',
    'stats' => [
        'title' => '统计信息',
        'number_of_seasons' => '{0, plural,
            one {# 季}
            other {# 季}
        }',
        'number_of_episodes' => '{0, plural,
            one {# 剧集}
            other {# 剧集}
        }',
        'first_published_at' => '首集发布于 {0, date, medium}',
    ],
    'sponsor' => '赞助者',
    'funding_links' => '{podcastTitle} 的赞助链接',
    'find_on' => '查找 {podcastTitle} 于',
    'listen_on' => '收听',
    'persons' => '{personsCount, plural,
        one {# 人}
        other {# 人}
    }',
    'persons_list' => '人',
    'castopod_website' => 'Castopod (website)',
];
