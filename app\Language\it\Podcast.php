<?php

declare(strict_types=1);

/**
 * @copyright  2020 Ad Aures
 * @license    https://www.gnu.org/licenses/agpl-3.0.en.html AGPL3
 * @link       https://castopod.org/
 */

return [
    'feed' => 'Feed RSS del podcast',
    'season' => 'Stagione {seasonNumber}',
    'list_of_episodes_year' => '{year} episodi ({episodeCount})',
    'list_of_episodes_season' =>
        'Stagione {seasonNumber} episodi ({episodeCount})',
    'no_episode' => 'Nessun episodio trovato!',
    'follow' => 'Segui',
    'followTitle' => 'Segui {actorDisplayName} sul fediverso!',
    'followers' => '{numberOfFollowers, plural,
        one {# follower}
        other {# followers}
    }',
    'posts' => '{numberOfPosts, plural,
        one {# post}
        other {# posts}
    }',
    'links' => 'Link',
    'activity' => 'Attività',
    'episodes' => 'Episodi',
    'episodes_title' => 'Episodi di {podcastTitle}',
    'about' => 'Informazioni',
    'stats' => [
        'title' => 'Stats',
        'number_of_seasons' => '{0, plural,
            one {# season}
            other {# seasons}
        }',
        'number_of_episodes' => '{0, plural,
            one {# episode}
            other {# episodes}
        }',
        'first_published_at' => 'Primo episodio pubblicato il {0, date, medium}',
    ],
    'sponsor' => 'Sponsor',
    'funding_links' => 'Link di finanziamento per {podcastTitle}',
    'find_on' => 'Trova {podcastTitle} su',
    'listen_on' => 'Ascolta su',
    'persons' => '{personsCount, plural,
        one {# person}
        other {# persons}
    }',
    'persons_list' => 'Persone',
    'castopod_website' => 'Castopod (sito web)',
];
