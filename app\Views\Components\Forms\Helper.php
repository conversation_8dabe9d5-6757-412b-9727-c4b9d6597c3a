<?php

declare(strict_types=1);

namespace App\Views\Components\Forms;

use Override;
use ViewComponents\Component;

class Helper extends Component
{
    // TODO: add type with error and show errors inline

    #[Override]
    public function render(): string
    {
        $this->mergeClass('form-helper');

        return <<<HTML
            <small {$this->getStringifiedAttributes()}>{$this->slot}</small>
        HTML;
    }
}
