<?php

declare(strict_types=1);

/**
 * @copyright  2020 Ad Aures
 * @license    https://www.gnu.org/licenses/agpl-3.0.en.html AGPL3
 * @link       https://castopod.org/
 */

return [
    'feed' => 'Fil RSS del podcast',
    'season' => 'Temporada {seasonNumber}',
    'list_of_episodes_year' => 'Episodis del {year} ({episodeCount})',
    'list_of_episodes_season' =>
        'Episodis de la temporada {seasonNumber} ({episodeCount})',
    'no_episode' => 'No s\'han trobat episodis.',
    'follow' => 'Seguir',
    'followTitle' => 'Seguir a {actorDisplayName} al Fediverse!',
    'followers' => '{numberOfFollowers, plural,
        one {# seguidor}
        other {# seguidors}
    }',
    'posts' => '{numberOfPosts, plural,
        one {# publicació}
        other {# publicacions}
    }',
    'links' => 'Enllaços',
    'activity' => 'Activitat',
    'episodes' => 'Episodis',
    'episodes_title' => 'Episodis de {podcastTitle}',
    'about' => 'Quant a',
    'stats' => [
        'title' => 'Estadístiques',
        'number_of_seasons' => '{0, plural,
            one {# temporada}
            other {# temporades}
        }',
        'number_of_episodes' => '{0, plural,
            one {# episodi}
            other {# episodis}
        }',
        'first_published_at' => 'El primer episodi va ser publicat el {0, date, medium}',
    ],
    'sponsor' => 'Patrocinador',
    'funding_links' => 'Enllaços de finançament per {podcastTitle}',
    'find_on' => 'Trobar {podcastTitle} a',
    'listen_on' => 'Escoltant',
    'persons' => '{personsCount, plural,
        one {# persona}
        other {# persones}
    }',
    'persons_list' => 'Persones',
    'castopod_website' => 'Castopod (website)',
];
