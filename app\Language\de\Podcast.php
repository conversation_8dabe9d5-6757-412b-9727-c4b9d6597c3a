<?php

declare(strict_types=1);

/**
 * @copyright  2020 Ad Aures
 * @license    https://www.gnu.org/licenses/agpl-3.0.en.html AGPL3
 * @link       https://castopod.org/
 */

return [
    'feed' => 'RSS-Feed',
    'season' => 'Staffel {seasonNumber}',
    'list_of_episodes_year' => '({episodeCount}) Episoden in {year}',
    'list_of_episodes_season' =>
        'Staffel {seasonNumber} Episode ({episodeCount})',
    'no_episode' => 'Keine Episode gefunden!',
    'follow' => 'Folgen',
    'followTitle' => 'Folge {actorDisplayName} im Fediversum!',
    'followers' => '{numberOfFollowers, plural,
        one {# Follower}
        other {# Follower}
    }',
    'posts' => '{numberOfPosts, plural,
        one {# Beitrag}
        other {# Beiträge}
    }',
    'links' => 'Links',
    'activity' => 'Aktivitäten',
    'episodes' => 'Episoden',
    'episodes_title' => '<PERSON><PERSON> von {podcastTitle}',
    'about' => 'Über',
    'stats' => [
        'title' => 'Statistiken',
        'number_of_seasons' => '{0, plural,
            one {# <PERSON>el}
            other {# Staffeln}
        }',
        'number_of_episodes' => '{0, plural,
            one {# Episode}
            other {# Episoden}
        }',
        'first_published_at' => 'Erste Episode veröffentlicht am {0, date, medium}',
    ],
    'sponsor' => 'Unterstützer',
    'funding_links' => 'Links zur Finanzierung von {podcastTitle}',
    'find_on' => 'Finde {podcastTitle} auf',
    'listen_on' => 'Hören auf',
    'persons' => '{personsCount, plural,
        one {# Person}
        other {# Personen}
    }',
    'persons_list' => 'Mitwirkende',
    'castopod_website' => 'Castopod (Webseite)',
];
