---
title: <PERSON><PERSON><PERSON><PERSON><PERSON> 👋
---

import { LinkCard } from "@astrojs/starlight/components";

Castopod je besplatna platforma otvorenog koda napravljena za podkastere koji
žele interakciju sa svojom publikom.

Castopod se lako instalira i napravljen je na
[CodeIgniter4](https://codeigniter.com/), moćnom PHP okviru sa jako malim
otiskom (footprint).

<LinkCard title="Instaliraj" href="./getting-started/install" />

## Funkcionalnosti

- 🌱 Besplatan i otvorenog koda (AGPL v3 License)
- 🔐 Fokusiran an suverenitet podataka: va<PERSON>, publika i analitika pripada
  vama i samo vama
- 🪄  Podkasting 2.0 funkcionalnosti: GUID, zaključan, transkripti, podrška,
  poglavlja, lokacija, posobe, zvuč<PERSON> ise<PERSON>, …
- 💬  Ugrađena društvena mreža:
  - 🚀  Castopod je deo Fediversa, decentralizovane društvene mreže
  - ❤️  Napravite objave, delite, dodajte u omiljene i komentarišite epizode
- 📈  Ugrađena analitika:
  - ⚖️  U skladu sa GDPR / CCPA / LGPD
  - 🪙  Merenje publike putem IABv2 standarda
  - 🏡  Analitika na licu mesta, bez uključenosti trećih strana
- 📢  Ugrađeni marketinški alati:
  - ✅  SEO spremno (open-graph meta-tags, JSON-LD, …)
  - 📱  PWA: instalirajte kao samostojeću aplikaciju
  - 🎨  Prilagodljive boje teme
  - 🎬  Napravite video isečke iz epizoda koji su spremni za deljenje
  - 🔉  Napravite audio isečke
  - ▶️  Plejer koji možete koristiti na svom sajtu (embed)
- 💸  Monetizacija:
  - 🔗  Linkovi za podršku publike
  - 📲  Slušaj i klikni reklame
  - 🤝  value4value / Veb Monetizacija
  - 💎  Premijum podkasti
- 📡  Objavite svoje epizode svugde uz RSS:
  - 📱  Na svim agregatorima i aplikacijama: Podcast Index, Apple Podcasts,
    Spotify, Google Podcasts, Deezer, Podcast Addict, Podfriend, …
  - ⚡  Emitujte svoje epizode instant uz WebSub
- 📥  Uvoz podkasta: prebacite svoj postojeći podkast na Castopod
- 📤  Prebacite svoj podkast sa Castopod-a
- 🔀  Mreža: hostujte koliko god želite podkasta
- 👥  Više korisnika: dodajte saradnike i odredite njihove uloge
- 🌎  i18n support: translated in English, French, Polish, German, Brazilian
  Portuguese, Spanish, simplified Chinese… and
  [many more](https://translate.castopod.org)!

## Motivacija

Ekosistem podcasta je po prirodi decentralizovan: možete kreirati svoj podkast
kao RSS datoteku, objavite je na vebu i deliti svuda na mreži.

To je zapravo jedan od retkih medija koji je ostao decentralizovan ovako dugo.

Kako se upotreba razvija, sve više ljudi ulazi u podkaste: ili kao kreatori koji
pronalaze nove načine da podele svoje ideje ili kao slušaoci u potrazi za boljim
sadržajem.

Kako podkasting postaje sve više korišćen, neke kompanije pokušavaju da ga
promene ka kontrolisanijem i centralizovanijem mediju.

Castopod je stvoren u nastojanju da pruži otvorenu i održivu alternativu za
hostovanje vaših podkasta, promovišući decentralizaciju kako bi se osiguralo da
podkasterska kreativnost može da se izrazi.

Ovaj projekat gura zajednica otvorenog koda, ponajviše
[Fedivers](https://fediverse.party/en/fediverse/) i
[Podcasting 2.0](https://podcastindex.org/) pokreti.

## Poređenje sa drugim rešenjima

We believe that a solution is not necessarily right for everyone, it highly
depends on your needs. So, here are comparisons with other tools to help you to
gauge whether Castopod is the right fit for you.

### Castopod protiv Wordpress-a

Castopod is often referred to as "the Wordpress for podcasts" because of the
similarities between the two. In some ways this is true. And actually, Castopod
was greatly inspired by the Wordpress ecosystem, seeing the ease of adoption
from the community and the number of websites running it.

Isto kao i Wordpress, Castopod je besplatan i otvorenog koda, napravljen
koristeći PHP i MySQL baze podataka i spakovan na način koji vam omogućava
jednostavnu instalaciju na većini veb servera.

Wordpress is a great way to create your website and extend it with plugins to
get what you want. It is a full fledged CMS that helps you get any type of
website online.

On the other hand, Castopod is meant to address the podcasters needs
specifically, focusing on podcasting, and nothing else. You don't need any
plugin to get you started on your podcasting journey.

Ovo omogućava optimizaciju procesa specifičnih za podcasting: u rasponu od
kreiranja vaših podkasta i objavljivanja novih epizoda sve do emitovanja,
marketinga i analitike.

Konačno, u zavisnosti od vaših potreba, Wordpress i Castopod mogu čak i da
funkcionišu zajedno jer dele iste zahteve!

### Castopod protiv Funkwhale-a

Funkwhale is a self-hosted, modern free and open-source music server. Just as
Castopod, Funkwhale is on the fediverse, a decentralized social network allowing
interoperability between the two.

Funkwhale was initially built around music. And later on, as the project
evolved, the ability to host podcasts was introduced.

Unlike Funkwhale, Castopod has been designed and built around podcasting
exclusively. This allows easier implementation for features related to the
podcasting ecosystem, such as the podcasting 2.0 features (transcripts,
chapters, locations, persons, …).

Tako da bi ste verovatno trebali da koristite Funkwhale ukoliko želite da
hostujete svoju muziku a Castopod ukoliko želite da hostujete svoje podkaste.

### Castopod protiv drugih podkast hosting-a

Postoji mnogo različitih rešenja za hostovanje vaših podkasta, neka od njih su
stvarno fantastična i [ dosta njih](https://podcastindex.org/apps) se
priključuje Podcasting 2.0 trendu, isto kao i Castopod!

Svako od ovih rešenja se razlikuje jedno od drugog, možete ih uporediti sa
[listom funkcionalnosti](#features).

Imajući to u vidu, postoje dve glavne razlike u odnosu na druga rešenja za
podkasting:

- Castopod can be self-hosted and is the only solution that allows you to keep
  full control over what you produce. Also, as it is open-source, you can even
  customize it as you wish.

- Castopod je jedino rešenje koje trenutno uklapa decentralizovanu društvenu
  mrežu sa ActivityPub-om kao i mnoge funkcionalnosti podkasting-a 2.0, nadajući
  se da će ih tako približiti.

## Doprinos

Love Castopod and would like to help? Take a look at the following documentation
to get you started.

### Kodeks ponašanja

Castopod has adopted a Code of Conduct that we expect project participants to
adhere to. Please read the
[CODE_OF_CONDUCT manual](https://code.castopod.org/adaures/castopod/-/blob/beta/CODE_OF_CONDUCT.md)
so that you can understand what actions will and will not be tolerated.

### Vodič za doprinos

Pročitajte naše [uputstvo za doprinos](../contributing/guidelines.md) kako bi
ste bolje razumeli naš proces razvoja, kako da predložite popravke bagova i
unapređenja i kako da napravite i testirate svoje promene na Castopod-u.

## Saradnici ✨

Veliko hvala ovim divnim ljudima
([ključ emotikona](https://allcontributors.org/docs/en/emoji-key)):

- [Discord](https://castopod.org/discord) (for direct interaction with
  developers and the community)
- [Issue tracker](https://code.castopod.org/adaures/castopod/-/issues) (for
  feature requests & bug reports)

Alternatively, you can follow us on social media platforms to get news about
Castopod:

- [podlibre.social](https://podlibre.social/@Castopod) (Mastodon instance)
- [Twitter](https://twitter.com/castopod)
- [LinkedIn](https://linkedin.com/company/castopod)
- [Facebook](https://www.facebook.com/castopod)

## Sponsors

The ongoing development of Castopod is made possible with the support of its
backers. If you'd like to help, please consider
[sponsoring Castopod's development](https://opencollective.com/castopod/contribute).

[![Ad Aures Logo](../../../assets/images/sponsors/adaures.svg)](https://adaures.com/)

[![NLnet Logo](../../../assets/images/sponsors/nlnet.svg)](https://nlnet.nl/)

## License

[GNU Affero General Public License v3.0](https://choosealicense.com/licenses/agpl-3.0/)

Copyright © 2020-present, [Ad Aures](https://adaures.com/).
