<?php

declare(strict_types=1);

/**
 * @copyright  2020 Ad Aures
 * @license    https://www.gnu.org/licenses/agpl-3.0.en.html AGPL3
 * @link       https://castopod.org/
 */

return [
    'feed' => 'Kanał RSS Podcastu',
    'season' => 'Sezon {seasonNumber}',
    'list_of_episodes_year' => '{year} odcinków ({episodeCount})',
    'list_of_episodes_season' =>
        'Sezon {seasonNumber} odcinki ({episodeCount})',
    'no_episode' => 'Nie znaleziono odcinków!',
    'follow' => 'Obserwuj',
    'followTitle' => 'Obserwuj {actorDisplayName} na fediwersum!',
    'followers' => '{numberOfFollowers, plural,
        one {# polubienie}
        few {# polubienia}
        other {# polubień}
    }',
    'posts' => '{numberOfPosts, plural,
        one {# osoba}
        few {# osoby}
        other {# osób}
    }',
    'links' => 'Linki',
    'activity' => 'Wpisy',
    'episodes' => 'Odcinki',
    'episodes_title' => 'Odcinki {podcastTitle}',
    'about' => 'Informacje',
    'stats' => [
        'title' => 'Statystyki',
        'number_of_seasons' => '{0, plural,
        one {# osoba}
        few {# osoby}
        other {# osób}
    }',
        'number_of_episodes' => '{0, plural,
        one {# osoba}
        few {# osoby}
        other {# osób}
    }',
        'first_published_at' => 'Pierwszy odcinek opublikowany {0, date, medium}',
    ],
    'sponsor' => 'Sponsoruj',
    'funding_links' => 'Linki finansowania dla {podcastTitle}',
    'find_on' => 'Znajdź {podcastTitle} na',
    'listen_on' => 'Słuchaj na',
    'persons' => '{personsCount, plural,
        one {# osoba}
        few {# osoby}
        other {# osób}
    }',
    'persons_list' => 'Osoby',
    'castopod_website' => 'Castopod (strona)',
];
