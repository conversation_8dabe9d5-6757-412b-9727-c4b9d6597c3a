<?php

declare(strict_types=1);

/**
 * @copyright  2020 Ad Aures
 * @license    https://www.gnu.org/licenses/agpl-3.0.en.html AGPL3
 * @link       https://castopod.org/
 */

return [
    'feed' => 'Gwazh RSS ar podkast',
    'season' => '<PERSON><PERSON><PERSON> {seasonNumber}',
    'list_of_episodes_year' => 'Rannoù {year} ({episodeCount})',
    'list_of_episodes_season' =>
        'Ranno<PERSON> koulzad {seasonNumber} ({episodeCount})',
    'no_episode' => 'N\'eo bet kavet rann ebet!',
    'follow' => 'Heuliañ',
    'followTitle' => 'Heuliañ {actorDisplayName} war ar fediverse!',
    'followers' => '{numberOfFollowers, plural,
        one {# heulier·ez}
        two {# heulier·ez}
        few {# heulier·ez}
        many {# heulier·ez}
        other {# heulier·ez}
    }',
    'posts' => '{numberOfPosts, plural,
        one {# gemennadenn}
        two {# gemennadenn}
        few {# c\'hemennadenn}
        many {# kemennadenn}
        other {# kemennadenn}
    }',
    'links' => '<PERSON>moù',
    'activity' => 'Obererezh',
    'episodes' => 'Rannoù',
    'episodes_title' => 'Rannoù {podcastTitle}',
    'about' => 'A-zivout',
    'stats' => [
        'title' => 'Stadegoù',
        'number_of_seasons' => '{0, plural,
            one {# c\'houlzad}
            two {# goulzad}
            few {# c\'houlzad}
            many {# koulzad}
            other {# koulzad}
        }',
        'number_of_episodes' => '{0, plural,
            one {# rann}
            two {# rann}
            few {# rann}
            many {# rann}
            other {# rann}
        }',
        'first_published_at' => 'Embannet eo bet ar rann gentañ d\'ar/d\'an {0, date, medium}',
    ],
    'sponsor' => 'Harpit',
    'funding_links' => 'Ereoù evit arc\'hantaouiñ {podcastTitle}',
    'find_on' => 'Kavit {podcastTitle} war',
    'listen_on' => 'Selaouit war',
    'persons' => '{personsCount, plural,
        one {# den}
        two {# zen}
        few {# den}
        many {# den}
        other {# den}
    }',
    'persons_list' => 'Emellerien·ezed',
    'castopod_website' => 'Castopod (lec\'hienn)',
];
