<?php

declare(strict_types=1);

/**
 * @copyright  2020 Ad Aures
 * @license    https://www.gnu.org/licenses/agpl-3.0.en.html AGPL3
 * @link       https://castopod.org/
 */

return [
    'feed' => 'Flux RSS del podcast',
    'season' => 'Sason {seasonNumber}',
    'list_of_episodes_year' => '{year} episòdis ({episodeCount})',
    'list_of_episodes_season' =>
        'Sason {seasonNumber} episòdis ({episodeCount})',
    'no_episode' => 'Cap d’episòdi pas trobat !',
    'follow' => 'Seguir',
    'followTitle' => 'Seguissètz {actorDisplayName} pell fedivèrs !',
    'followers' => '{numberOfFollowers, plural,
        one {# abonat}
        other {# abonats}
    }',
    'posts' => '{numberOfPosts, plural,
        one {# publicacion}
        other {# publicacions}
    }',
    'links' => 'Ligams',
    'activity' => 'Activitat',
    'episodes' => 'Episòdis',
    'episodes_title' => 'Episòdi de {podcastTitle}',
    'about' => 'A prepaus',
    'stats' => [
        'title' => 'Estatisticas',
        'number_of_seasons' => '{0, plural,
            one {# sason}
            other {# sasons}
        }',
        'number_of_episodes' => '{0, plural,
            one {# episòdi}
            other {# episòdis}
        }',
        'first_published_at' => 'Primièr episòdi publicat lo {0, date, medium}',
    ],
    'sponsor' => 'Sosténer',
    'funding_links' => 'Ligams de finançament per {podcastTitle}',
    'find_on' => 'Trobatz {podcastTitle} sus',
    'listen_on' => 'Escotar sus',
    'persons' => '{personsCount, plural,
        one {# persona}
        other {# personas}
    }',
    'persons_list' => 'Personas',
    'castopod_website' => 'Castopod (website)',
];
