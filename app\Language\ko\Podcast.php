<?php

declare(strict_types=1);

/**
 * @copyright  2020 Ad Aures
 * @license    https://www.gnu.org/licenses/agpl-3.0.en.html AGPL3
 * @link       https://castopod.org/
 */

return [
    'feed' => 'RSS Podcast feed',
    'season' => 'Season {seasonNumber}',
    'list_of_episodes_year' => '{year} episodes ({episodeCount})',
    'list_of_episodes_season' =>
        'Season {seasonNumber} episodes ({episodeCount})',
    'no_episode' => 'No episode found!',
    'follow' => 'Follow',
    'followTitle' => 'Follow {actorDisplayName} on the fediverse!',
    'followers' => '{numberOfFollowers, plural,
        one {# follower}
        other {# followers}
    }',
    'posts' => '{numberOfPosts, plural,
        one {# post}
        other {# posts}
    }',
    'links' => 'Links',
    'activity' => 'Activity',
    'episodes' => 'Episodes',
    'episodes_title' => 'Episodes of {podcastTitle}',
    'about' => 'About',
    'stats' => [
        'title' => 'Stats',
        'number_of_seasons' => '{0, plural,
            one {# season}
            other {# seasons}
        }',
        'number_of_episodes' => '{0, plural,
            one {# episode}
            other {# episodes}
        }',
        'first_published_at' => 'First episode published on {0, date, medium}',
    ],
    'sponsor' => 'Sponsor',
    'funding_links' => 'Funding links for {podcastTitle}',
    'find_on' => 'Find {podcastTitle} on',
    'listen_on' => 'Listen on',
    'persons' => '{personsCount, plural,
        one {# person}
        other {# persons}
    }',
    'persons_list' => 'Persons',
    'castopod_website' => 'Castopod (website)',
];
