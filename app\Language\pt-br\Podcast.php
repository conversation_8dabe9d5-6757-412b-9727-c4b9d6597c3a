<?php

declare(strict_types=1);

/**
 * @copyright  2020 Ad Aures
 * @license    https://www.gnu.org/licenses/agpl-3.0.en.html AGPL3
 * @link       https://castopod.org/
 */

return [
    'feed' => 'Feed RSS do podcast',
    'season' => 'Temporada {seasonNumber}',
    'list_of_episodes_year' => 'Episódios de {year} ({episodeCount})',
    'list_of_episodes_season' =>
        'Episódios da temporada {seasonNumber} ({episodeCount})',
    'no_episode' => 'Nenhum episódio encontrado!',
    'follow' => 'Seguir',
    'followTitle' => 'Siga {actorDisplayName} no fediverso!',
    'followers' => '{numberOfFollowers, plural,
        one {# seguidor}
        other {# seguidores}
    }',
    'posts' => '{numberOfPosts, plural,
        one {# publicação}
        other {# publicações}
    }',
    'links' => 'Links',
    'activity' => 'Atividade',
    'episodes' => 'Episódios',
    'episodes_title' => 'Episódios de {podcastTitle}',
    'about' => 'Sobre',
    'stats' => [
        'title' => 'Estatísticas',
        'number_of_seasons' => '{0, plural,
            one {# temporada}
            other {# temporadas}
        }',
        'number_of_episodes' => '{0, plural,
            one {# episódio}
            other {# episódios}
        }',
        'first_published_at' => 'Primeiro episódio publicado em {0, date, medium}',
    ],
    'sponsor' => 'Apoiar',
    'funding_links' => 'Links de financiamento para {podcastTitle}',
    'find_on' => 'Encontrar {podcastTitle} em',
    'listen_on' => 'Ouvir em',
    'persons' => '{personsCount, plural,
        one {# pessoa}
        other {# pessoas}
    }',
    'persons_list' => 'Pessoas',
    'castopod_website' => 'Castopod (website)',
];
