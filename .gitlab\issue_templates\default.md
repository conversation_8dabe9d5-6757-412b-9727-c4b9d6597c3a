### Before submitting an issue

1. **Use the issue search** &mdash; check if the issue has already been
   reported.

2. **Check if the issue has been fixed** &mdash; try to reproduce it using the
   latest release.

3. **Isolate the problem** &mdash; ideally create a
   [reduced test case](https://css-tricks.com/reduced-test-cases/) and a live
   example.

4. **Select an issue template** &mdash; choose a template from `bug` or
   `feature-request` and fill out the info you deem necessary. The more context
   we get, the easier it is to implement the feature or fix the bug you report.

Check out the [CONTRIBUTING manual](../../CONTRIBUTING.md) for more info.
