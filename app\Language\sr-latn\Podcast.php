<?php

declare(strict_types=1);

/**
 * @copyright  2020 Ad Aures
 * @license    https://www.gnu.org/licenses/agpl-3.0.en.html AGPL3
 * @link       https://castopod.org/
 */

return [
    'feed' => 'RSS Podcast snabdevanje',
    'season' => 'Sezona {seasonNumber}',
    'list_of_episodes_year' => '{year} epizode ({episodeCount})',
    'list_of_episodes_season' =>
        'Sezona {seasonNumber} epizoda ({episodeCount})',
    'no_episode' => 'Nijedna epizode nije pronađena!',
    'follow' => 'Prati',
    'followTitle' => 'Prati {actorDisplayName} na fediverse!',
    'followers' => '{numberOfFollowers, plural,
        one {# pratioc}
        other {# pratilaca}
    }',
    'posts' => '{numberOfPosts, plural,
        few {# objave}
        other {# objava}
    }',
    'links' => 'Linkovi',
    'activity' => 'Aktivnost',
    'episodes' => 'Epizode',
    'episodes_title' => 'Epizode {podcastTitle}',
    'about' => 'Osnovni podaci',
    'stats' => [
        'title' => 'Statistika',
        'number_of_seasons' => '{0, plural,
            few {# sezone}
            other {# sezona}
        }',
        'number_of_episodes' => '{0, plural,
            one {# epizoda}
            other {# epizode}
        }',
        'first_published_at' => 'Prva epizoda objavljena na {0, date, medium}',
    ],
    'sponsor' => 'Sponzor',
    'funding_links' => 'Linkovi za finansiranje {podcastTitle}',
    'find_on' => 'Pronađi {podcastTitle} na',
    'listen_on' => 'Slušaj na',
    'persons' => '{personsCount, plural,
        one {# osoba}
        other {# osobe}
    }',
    'persons_list' => 'Ličnosti',
    'castopod_website' => 'Castopod (veb stranica)',
];
