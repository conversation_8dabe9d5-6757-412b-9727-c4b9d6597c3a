<?php

declare(strict_types=1);

/**
 * @copyright  2020 Ad Aures
 * @license    https://www.gnu.org/licenses/agpl-3.0.en.html AGPL3
 * @link       https://castopod.org/
 */

return [
    'feed' => 'Flux RSS du Podcast',
    'season' => 'Saison {seasonNumber}',
    'list_of_episodes_year' => 'Épisodes de {year} ({episodeCount})',
    'list_of_episodes_season' =>
        'Épisodes de la saison {seasonNumber} ({episodeCount})',
    'no_episode' => 'Aucun épisode trouvé  !',
    'follow' => 'Suivre',
    'followTitle' => 'Suivre {actorDisplayName} sur le fédiverse  !',
    'followers' => '{numberOfFollowers, plural,
        one {# abonné}
        other {# abonnés}
    }',
    'posts' => '{numberOfPosts, plural,
        one {# publication}
        other {# publications}
    }',
    'links' => 'Links',
    'activity' => 'Activité',
    'episodes' => 'Épisodes',
    'episodes_title' => 'Épisodes de {podcastTitle}',
    'about' => 'À propos',
    'stats' => [
        'title' => 'Statistiques',
        'number_of_seasons' => '{0, plural,
            one {# saison}
            other {# saisons}
        }',
        'number_of_episodes' => '{0, plural,
            one {# épisode}
            other {# épisodes}
        }',
        'first_published_at' => 'Premier épisode publié le {0, date, medium}',
    ],
    'sponsor' => 'Soutenir',
    'funding_links' => 'Liens de financement pour {podcastTitle}',
    'find_on' => 'Trouvez {podcastTitle} sur',
    'listen_on' => 'Écoutez sur',
    'persons' => '{personsCount, plural,
        one {# intervenant}
        other {# intervenants}
    }',
    'persons_list' => 'Intervenants',
    'castopod_website' => 'Castopod (website)',
];
